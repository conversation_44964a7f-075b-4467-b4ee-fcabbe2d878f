"use client";

import { useEffect, useState } from "react";
import { Separator } from "@/components/ui/separator";

interface FooterProps {
  activeSection: string;
}

export function Footer({ activeSection }: FooterProps) {
  const [currentTime, setCurrentTime] = useState<Date | null>(null);
  const [loginTime, setLoginTime] = useState<Date | null>(null);
  const [sessionDuration, setSessionDuration] = useState("00:00:00");
  const [userIP, setUserIP] = useState("Loading...");
  const [isClient, setIsClient] = useState(false);

  // Initialize client-side only data
  useEffect(() => {
    setIsClient(true);
    const now = new Date();
    setCurrentTime(now);
    setLoginTime(now);
  }, []);

  // Update current time every second
  useEffect(() => {
    if (!isClient) return;

    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 1000);

    return () => clearInterval(timer);
  }, [isClient]);

  // Calculate session duration
  useEffect(() => {
    if (!isClient || !loginTime) return;

    const timer = setInterval(() => {
      const now = new Date();
      const diff = now.getTime() - loginTime.getTime();
      const hours = Math.floor(diff / (1000 * 60 * 60));
      const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));
      const seconds = Math.floor((diff % (1000 * 60)) / 1000);

      setSessionDuration(
        `${hours.toString().padStart(2, "0")}:${minutes
          .toString()
          .padStart(2, "0")}:${seconds.toString().padStart(2, "0")}`
      );
    }, 1000);

    return () => clearInterval(timer);
  }, [isClient, loginTime]);

  // Get user IP (mock implementation)
  useEffect(() => {
    if (!isClient) return;

    // In a real application, you would fetch this from an API
    // For demo purposes, we'll use a mock IP
    setTimeout(() => {
      setUserIP("*************");
    }, 1000);
  }, [isClient]);

  // Show loading state during SSR and initial hydration
  if (!isClient || !currentTime || !loginTime) {
    return (
      <footer className="border-t bg-background">
        <div className="container mx-auto px-4 py-3">
          <div className="flex flex-col sm:flex-row items-center justify-between gap-2 text-sm text-muted-foreground">
            <div className="flex items-center gap-4">
              <span className="font-medium">
                Current Section: {activeSection}
              </span>
              <Separator orientation="vertical" className="h-4" />
              <span>IP: Loading...</span>
            </div>

            <div className="flex items-center gap-4">
              <span>Login: --:--:--</span>
              <Separator orientation="vertical" className="h-4" />
              <span>Session: 00:00:00</span>
              <Separator orientation="vertical" className="h-4" />
              <span>Loading...</span>
            </div>
          </div>
        </div>
      </footer>
    );
  }

  return (
    <footer className="border-t bg-background">
      <div className="container mx-auto px-4 py-3">
        <div className="flex flex-col sm:flex-row items-center justify-between gap-2 text-sm text-muted-foreground">
          <div className="flex items-center gap-4">
            <span className="font-medium">
              Current Section: {activeSection}
            </span>
            <Separator orientation="vertical" className="h-4" />
            <span>IP: {userIP}</span>
          </div>

          <div className="flex items-center gap-4">
            <span>Login: {loginTime.toLocaleTimeString()}</span>
            <Separator orientation="vertical" className="h-4" />
            <span>Session: {sessionDuration}</span>
            <Separator orientation="vertical" className="h-4" />
            <span>{currentTime.toLocaleString()}</span>
          </div>
        </div>
      </div>
    </footer>
  );
}
