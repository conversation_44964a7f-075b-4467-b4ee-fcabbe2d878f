// main.go - Entry point của ứng dụng
package main

import (
	"fmt"
	"go-api/database"
	"go-api/routes"
	"log"
	"os"

	"github.com/gin-gonic/gin"
	"github.com/joho/godotenv"
)

func main() {
	// ========== BƯỚC 1: LOAD ENVIRONMENT VARIABLES ==========
	// Tải file .env để đọc cấu hình database, JWT secret, etc.
	if err := godotenv.Load(); err != nil {
		log.Println("⚠️  Không tìm thấy file .env, sử dụng environment variables củ<PERSON> hệ thống")
	}

	// ========== BƯỚC 2: THIẾT LẬP LOGGING ==========
	// Cấu hình log level dựa trên môi trường
	env := os.Getenv("APP_ENV")
	if env == "production" {
		gin.SetMode(gin.ReleaseMode) // Tắt debug logs trong production
		log.SetFlags(log.LstdFlags | log.Lshortfile)
	} else {
		gin.SetMode(gin.DebugMode) // Enable debug logs cho development
	}

	// ========== BƯỚC 3: KẾT NỐI DATABASE ==========
	log.Println("🔄 Đang kết nối đến SQL Server...")
	database.Connect()
	defer func() {
		// Đảm bảo đóng kết nối database khi ứng dụng tắt
		if database.DB != nil {
			database.DB.Close()
			log.Println("✅ Đã đóng kết nối database")
		}
	}()

	// ========== BƯỚC 4: KHỞI TẠO GIN ROUTER ==========
	router := gin.New()

	// Thêm middleware tùy chỉnh
	router.Use(gin.LoggerWithFormatter(customLogFormatter()))

	// ========== BƯỚC 5: ĐĂNG KÝ ROUTES ==========
	log.Println("🔄 Đang đăng ký routes...")
	routes.RegisterRoutes(router)

	// ========== BƯỚC 6: KHỞI ĐỘNG SERVER ==========
	port := os.Getenv("PORT")
	if port == "" {
		port = "8080" // Port mặc định
	}

	log.Printf("🚀 Server đang chạy trên port %s", port)
	log.Printf("🌐 Health check: http://localhost:%s/health", port)
	log.Printf("📚 API Base URL: http://localhost:%s/api", port)
	
	// Chạy server với graceful shutdown
	if err := router.Run(":" + port); err != nil {
		log.Fatal("❌ Lỗi khởi động server:", err)
	}
}

// customLogFormatter - Định dạng log đẹp hơn
func customLogFormatter() gin.LogFormatter {
	return func(param gin.LogFormatterParams) string {
		return fmt.Sprintf("📝 %s - [%s] \"%s %s %s %d %s \"%s\" %s\"\n",
			param.ClientIP,
			param.TimeStamp.Format("2006/01/02 - 15:04:05"),
			param.Method,
			param.Path,
			param.Request.Proto,
			param.StatusCode,
			param.Latency,
			param.Request.UserAgent(),
			param.ErrorMessage,
		)
	}
}