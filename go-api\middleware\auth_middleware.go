package middleware

import (
	"go-api/utils"
	"net/http"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/golang-jwt/jwt/v5"
)

// AuthMiddleware - Middleware kiểm tra JWT token trong header
func AuthMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		// Bước 1: Lấy Authorization header
		authHeader := c.GetHeader("Authorization")
		if authHeader == "" {
			c.JSON(http.StatusUnauthorized, gin.H{
				"error": "Authorization header bị thiếu",
			})
			c.Abort() // Dừng request
			return
		}

		// Bước 2: Kiểm tra format "Bearer <token>"
		tokenParts := strings.Split(authHeader, " ")
		if len(tokenParts) != 2 || tokenParts[0] != "Bearer" {
			c.JSON(http.StatusUnauthorized, gin.H{
				"error": "Format token không hợp lệ. Sử dụng: Bearer <token>",
			})
			c.Abort()
			return
		}

		// Bước 3: Lấy token string
		tokenString := tokenParts[1]
		
		// Bước 4: Validate token
		token, err := utils.ValidateToken(tokenString)
		if err != nil || !token.Valid {
			c.JSON(http.StatusUnauthorized, gin.H{
				"error": "Token không hợp lệ hoặc đã hết hạn",
			})
			c.Abort()
			return
		}

		// Bước 5: Lưu thông tin user vào context để handlers sử dụng
		if claims, ok := token.Claims.(jwt.MapClaims); ok {
			// Kiểm tra loại token
			if tokenType, ok := claims["type"].(string); ok && tokenType == "access" {
				c.Set("userid", claims["sub"]) // Lưu userid vào context
			} else {
				c.JSON(http.StatusUnauthorized, gin.H{
					"error": "Token type không hợp lệ",
				})
				c.Abort()
				return
			}
		}

		// Tiếp tục xử lý request
		c.Next()
	}
}