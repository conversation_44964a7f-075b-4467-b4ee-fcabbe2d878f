// models/user.go - <PERSON><PERSON>nh nghĩa cấu trúc dữ liệu User
package models

import (
	"time"
)

// User struct chỉ chứa những field cần thiết cho response
type User struct {
	ID        int       `json:"id" db:"id"`                 // ID người dùng
	UserID    string    `json:"userid" db:"userid"`         // Tên đăng nhập
	FullName  string    `json:"fullname" db:"fullname"`     // Họ tên đầy đủ
	Email     string    `json:"email" db:"email"`           // Email
	CreatedAt time.Time `json:"created_at" db:"created_at"` // Thời gian tạo
	IsActive  bool      `json:"is_active" db:"is_active"`   // Trạng thái kích hoạt
	Role      string    `json:"role" db:"role"`             // Vai trò người dùng
}

// UserAuth struct - Chỉ dùng cho authentication (có password_hash và refresh_token)
type User<PERSON>uth struct {
	ID                  int        `db:"id"`
	UserID              string     `db:"userid"`
	PasswordHash        string     `db:"password_hash"`
	FullName            string     `db:"fullname"`
	Email               string     `db:"email"`
	CreatedAt           time.Time  `db:"created_at"`
	IsActive            bool       `db:"is_active"`
	RefreshToken        *string    `db:"refresh_token"`
	RefreshTokenExpires *time.Time `db:"refresh_token_expires"`
	Role                string     `db:"role"`
}

// ToUser - Chuyển UserAuth thành User (loại bỏ thông tin nhạy cảm)
func (ua *UserAuth) ToUser() User {
	return User{
		ID:        ua.ID,
		UserID:    ua.UserID,
		FullName:  ua.FullName,
		Email:     ua.Email,
		CreatedAt: ua.CreatedAt,
		IsActive:  ua.IsActive,
		Role:      ua.Role,
	}
}

// RegisterRequest - Dữ liệu yêu cầu đăng ký
type RegisterRequest struct {
	UserID   string `json:"userid" binding:"required,min=3,max=50"`    // Tên đăng nhập: bắt buộc, 3-50 ký tự
	Password string `json:"password" binding:"required,min=6"`         // Mật khẩu: bắt buộc, tối thiểu 6 ký tự
	FullName string `json:"fullname" binding:"required,max=100"`       // Họ tên: bắt buộc, tối đa 100 ký tự
	Email    string `json:"email" binding:"required,email,max=100"`    // Email: bắt buộc, format email hợp lệ
}

// LoginRequest - Dữ liệu yêu cầu đăng nhập
type LoginRequest struct {
	UserID   string `json:"userid" binding:"required"`    // Tên đăng nhập: bắt buộc
	Password string `json:"password" binding:"required"`  // Mật khẩu: bắt buộc
}

// AuthResponse - Dữ liệu phản hồi sau khi đăng nhập thành công
type AuthResponse struct {
	AccessToken string `json:"access_token"` // Token để truy cập API (ngắn hạn)
	User        User   `json:"user"`         // Thông tin người dùng (chỉ fields cần thiết)
	// RefreshToken sẽ được set vào cookie thay vì response body
}