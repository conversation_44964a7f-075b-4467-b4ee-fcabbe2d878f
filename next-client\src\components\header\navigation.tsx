"use client";

import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import {
  Settings,
  Users,
  Clock,
  Calculator,
  FileText,
  BarChart3,
  BookOpen,
  Briefcase,
  Heart,
  Menu,
  ChevronDown,
  ChevronRight,
} from "lucide-react";
import { Sheet, SheetContent, SheetTrigger } from "@/components/ui/sheet";
import { useState } from "react";
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible";



interface NavigationProps {
  activeSection: string;
  onSectionChange: (section: string) => void;
}
const navigationItems = [
  {
    name: "N1 HỆ THỐNG",
    code: "N1",
    items: [
      { name: "N11 C<PERSON>u <PERSON>nh <PERSON>", code: "N11" },
      { name: "N12 Quản Lý Người Dùng", code: "N12" },
      { name: "N13 Phân Quyền", code: "N13" },
      { name: "N14 <PERSON><PERSON>", code: "N14" },
    ],
  },
  {
    name: "N2 DANH MỤC",
    code: "N2",
    items: [
      { name: "N21 Danh Mục Phòng Ban", code: "N21" },
      { name: "N22 Danh Mục Chức Vụ", code: "N22" },
      { name: "N23 Danh Mục Ca Làm", code: "N23" },
      { name: "N24 Danh Mục Loại Nghỉ", code: "N24" },
    ],
  },
  {
    name: "N3 NHÂN SỰ",
    code: "N3",
    items: [
      {
        name: "N31 Điểm Danh Hàng Ngày",
        code: "N31",
        subItems: [
          { name: "N311 Điểm Danh", code: "N311" },
          { name: "N312 Điều Động Hàng Tháng", code: "N312" },
        ],
      },
      { name: "N32 Nhân Sự", code: "N32" },
      { name: "N33 Hợp Đồng", code: "N33" },
      { name: "N34 Bình Bầu", code: "N34" },
      { name: "N35 Nghỉ Phép", code: "N35" },
      { name: "N36 Giải Quyết Thôi Việc", code: "N36" },
      { name: "N37 Ký Luật Lao Động", code: "N37" },
      { name: "N38 Cập Nhật Nhà Xưởng", code: "N38" },
      { name: "N39 Lý Lịch Nhân Viên", code: "N39" },
      { name: "N3A Chuyển Đơn Vị", code: "N3A" },
      { name: "N3B Nơi Đăng Ký Khám Chữa Bệnh", code: "N3B" },
      { name: "N3C Chuyển Công Việc", code: "N3C" },
      { name: "N3D Danh Sách Cát Lượng NNDH", code: "N3D" },
      { name: "N3E Đề Nghị Làm Lại Thẻ Từ", code: "N3E" },
      { name: "N3G Theo dõi Lương Bình bầu CNV Thôi Việc", code: "N3G" },
      { name: "N3H Thống Kê KPI", code: "N3H" },
      { name: "N3K Sổ Quản Lý Lao Động", code: "N3K" },
      { name: "N3L Tài Nạn Lao Động", code: "N3L" },
    ],
  },
  {
    name: "N4 CHẤM CÔNG",
    code: "N4",
    items: [
      { name: "N41 Chấm Công Hàng Ngày", code: "N41" },
      { name: "N42 Tổng Hợp Chấm Công", code: "N42" },
      { name: "N43 Báo Cáo Chấm Công", code: "N43" },
    ],
  },
  {
    name: "N5 TÍNH LƯƠNG",
    code: "N5",
    items: [
      { name: "N51 Bảng Lương Cơ Bản", code: "N51" },
      { name: "N52 Tính Lương Tháng", code: "N52" },
      { name: "N53 Phụ Cấp", code: "N53" },
      { name: "N54 Thưởng Phạt", code: "N54" },
    ],
  },
  {
    name: "N6 BÁO CÁO",
    code: "N6",
    items: [
      { name: "N61 Báo Cáo Nhân Sự", code: "N61" },
      { name: "N62 Báo Cáo Chấm Công", code: "N62" },
      { name: "N63 Báo Cáo Lương", code: "N63" },
      { name: "N64 Báo Cáo Tổng Hợp", code: "N64" },
    ],
  },
  {
    name: "N7 TÀI LIỆU",
    code: "N7",
    items: [
      { name: "N71 Hướng Dẫn Sử Dụng", code: "N71" },
      { name: "N72 Quy Trình Nghiệp Vụ", code: "N72" },
      { name: "N73 Biểu Mẫu", code: "N73" },
    ],
  },
  {
    name: "N8 QUẢN LÝ",
    code: "N8",
    items: [
      { name: "N81 Quản Lý Dự Án", code: "N81" },
      { name: "N82 Quản Lý Tài Sản", code: "N82" },
      { name: "N83 Quản Lý Kho", code: "N83" },
    ],
  },
  {
    name: "N9 Y TẾ",
    code: "N9",
    items: [
      { name: "N91 Khám Sức Khỏe", code: "N91" },
      { name: "N92 Theo Dõi Sức Khỏe", code: "N92" },
      { name: "N93 Báo Cáo Y Tế", code: "N93" },
    ],
  },
];

export function Navigation({
  activeSection,
  onSectionChange,
}: NavigationProps) {
  const [isOpen, setIsOpen] = useState(false);

  const NavigationContent = () => (
    <>
      {navigationItems.map((item) => {
        return (
          <Button
            key={item.name}
            variant={activeSection === item.name ? "default" : "ghost"}
            className={cn(
              "justify-start gap-2",
              activeSection === item.name &&
                "bg-primary text-primary-foreground"
            )}
            onClick={() => {
              onSectionChange(item.name);
              setIsOpen(false);
            }}
          >
            <span className="hidden sm:inline">{item.name}</span>
          </Button>
        );
      })}
    </>
  );

  return (
    <>
      {/* Desktop Navigation */}
      <nav className="hidden md:flex items-center space-x-2">
        <NavigationContent />
      </nav>

      {/* Mobile Navigation */}
      <Sheet open={isOpen} onOpenChange={setIsOpen}>
        <SheetTrigger asChild>
          <Button variant="ghost" size="icon" className="md:hidden">
            <Menu className="h-5 w-5" />
          </Button>
        </SheetTrigger>
        <SheetContent side="left" className="w-64">
          <div className="flex flex-col space-y-2 mt-8">
            <NavigationContent />
          </div>
        </SheetContent>
      </Sheet>
    </>
  );
}
