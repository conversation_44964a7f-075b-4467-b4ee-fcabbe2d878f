"use client";

import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import {
  Settings,
  Users,
  Clock,
  Calculator,
  FileText,
  BarChart3,
  BookOpen,
  Briefcase,
  Heart,
  Menu,
  ChevronDown,
  ChevronRight,
} from "lucide-react";
import { Sheet, SheetContent, SheetTrigger } from "@/components/ui/sheet";
import { useState } from "react";

interface NavigationProps {
  activeSection: string;
  onSectionChange: (section: string) => void;
}

// Icon mapping for each navigation section
const getIconForSection = (code: string) => {
  const iconMap: {
    [key: string]: React.ComponentType<{ className?: string }>;
  } = {
    N1: Settings,
    N2: FileText,
    N3: Users,
    N4: Clock,
    N5: Calculator,
    N6: BarChart3,
    N7: BookOpen,
    N8: Briefcase,
    N9: Heart,
  };
  return iconMap[code] || FileText;
};

const navigationItems = [
  {
    name: "N1 HỆ THỐNG",
    code: "N1",
    items: [
      { name: "N11 <PERSON><PERSON><PERSON>", code: "N11" },
      { name: "N12 Quản L<PERSON>ờ<PERSON>", code: "N12" },
      { name: "N13 Phân Quyền", code: "N13" },
      { name: "N14 Sao Lưu Dữ Liệu", code: "N14" },
    ],
  },
  {
    name: "N2 DANH MỤC",
    code: "N2",
    items: [
      { name: "N21 Danh Mục Phòng Ban", code: "N21" },
      { name: "N22 Danh Mục Chức Vụ", code: "N22" },
      { name: "N23 Danh Mục Ca Làm", code: "N23" },
      { name: "N24 Danh Mục Loại Nghỉ", code: "N24" },
    ],
  },
  {
    name: "N3 NHÂN SỰ",
    code: "N3",
    items: [
      {
        name: "N31 Điểm Danh Hàng Ngày",
        code: "N31",
        subItems: [
          { name: "N311 Điểm Danh", code: "N311" },
          { name: "N312 Điều Động Hàng Tháng", code: "N312" },
        ],
      },
      { name: "N32 Nhân Sự", code: "N32" },
      { name: "N33 Hợp Đồng", code: "N33" },
      { name: "N34 Bình Bầu", code: "N34" },
      { name: "N35 Nghỉ Phép", code: "N35" },
      { name: "N36 Giải Quyết Thôi Việc", code: "N36" },
      { name: "N37 Ký Luật Lao Động", code: "N37" },
      { name: "N38 Cập Nhật Nhà Xưởng", code: "N38" },
      { name: "N39 Lý Lịch Nhân Viên", code: "N39" },
      { name: "N3A Chuyển Đơn Vị", code: "N3A" },
      { name: "N3B Nơi Đăng Ký Khám Chữa Bệnh", code: "N3B" },
      { name: "N3C Chuyển Công Việc", code: "N3C" },
      { name: "N3D Danh Sách Cát Lượng NNDH", code: "N3D" },
      { name: "N3E Đề Nghị Làm Lại Thẻ Từ", code: "N3E" },
      { name: "N3G Theo dõi Lương Bình bầu CNV Thôi Việc", code: "N3G" },
      { name: "N3H Thống Kê KPI", code: "N3H" },
      { name: "N3K Sổ Quản Lý Lao Động", code: "N3K" },
      { name: "N3L Tài Nạn Lao Động", code: "N3L" },
    ],
  },
  {
    name: "N4 CHẤM CÔNG",
    code: "N4",
    items: [
      { name: "N41 Chấm Công Hàng Ngày", code: "N41" },
      { name: "N42 Tổng Hợp Chấm Công", code: "N42" },
      { name: "N43 Báo Cáo Chấm Công", code: "N43" },
    ],
  },
  {
    name: "N5 TÍNH LƯƠNG",
    code: "N5",
    items: [
      { name: "N51 Bảng Lương Cơ Bản", code: "N51" },
      { name: "N52 Tính Lương Tháng", code: "N52" },
      { name: "N53 Phụ Cấp", code: "N53" },
      { name: "N54 Thưởng Phạt", code: "N54" },
    ],
  },
  {
    name: "N6 BÁO CÁO",
    code: "N6",
    items: [
      { name: "N61 Báo Cáo Nhân Sự", code: "N61" },
      { name: "N62 Báo Cáo Chấm Công", code: "N62" },
      { name: "N63 Báo Cáo Lương", code: "N63" },
      { name: "N64 Báo Cáo Tổng Hợp", code: "N64" },
    ],
  },
  {
    name: "N7 TÀI LIỆU",
    code: "N7",
    items: [
      { name: "N71 Hướng Dẫn Sử Dụng", code: "N71" },
      { name: "N72 Quy Trình Nghiệp Vụ", code: "N72" },
      { name: "N73 Biểu Mẫu", code: "N73" },
    ],
  },
  {
    name: "N8 QUẢN LÝ",
    code: "N8",
    items: [
      { name: "N81 Quản Lý Dự Án", code: "N81" },
      { name: "N82 Quản Lý Tài Sản", code: "N82" },
      { name: "N83 Quản Lý Kho", code: "N83" },
    ],
  },
  {
    name: "N9 Y TẾ",
    code: "N9",
    items: [
      { name: "N91 Khám Sức Khỏe", code: "N91" },
      { name: "N92 Theo Dõi Sức Khỏe", code: "N92" },
      { name: "N93 Báo Cáo Y Tế", code: "N93" },
    ],
  },
];

export function Navigation({
  activeSection,
  onSectionChange,
}: NavigationProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [expandedSections, setExpandedSections] = useState<string[]>([]);

  const toggleSection = (sectionCode: string) => {
    setExpandedSections((prev) =>
      prev.includes(sectionCode)
        ? prev.filter((code) => code !== sectionCode)
        : [...prev, sectionCode]
    );
  };

  const NavigationContent = () => (
    <div className="w-full bg-white">
      {navigationItems.map((section) => {
        const Icon = getIconForSection(section.code);
        const isExpanded = expandedSections.includes(section.code);

        return (
          <div key={section.code} className="border-b border-gray-100">
            {/* Main Section Header - WinForms Style */}
            <div
              className={cn(
                "flex items-center justify-between px-3 py-2 cursor-pointer",
                "bg-gradient-to-r from-blue-600 to-blue-700 text-white",
                "hover:from-blue-700 hover:to-blue-800 transition-all duration-200",
                "border-t border-blue-500 shadow-sm"
              )}
              onClick={() => toggleSection(section.code)}
            >
              <div className="flex items-center gap-2">
                <Icon className="h-4 w-4 text-blue-100" />
                <span className="font-medium text-sm">{section.name}</span>
              </div>
              <div className="text-blue-100">
                {isExpanded ? (
                  <ChevronDown className="h-4 w-4" />
                ) : (
                  <ChevronRight className="h-4 w-4" />
                )}
              </div>
            </div>

            {/* Submenu Items - WinForms Style */}
            {isExpanded && (
              <div className="bg-gray-50">
                {section.items.map((item) => (
                  <div key={item.code}>
                    <div
                      className={cn(
                        "flex items-center px-6 py-2 cursor-pointer text-sm",
                        "hover:bg-blue-100 hover:text-blue-800 transition-colors",
                        "border-l-4 border-transparent hover:border-blue-400",
                        activeSection === item.code &&
                          "bg-blue-200 text-blue-900 border-blue-500 font-medium"
                      )}
                      onClick={() => {
                        onSectionChange(item.code);
                        setIsOpen(false);
                      }}
                    >
                      <div className="w-2 h-2 bg-gray-400 rounded-full mr-3"></div>
                      <span className="truncate">{item.name}</span>
                    </div>

                    {/* Sub-submenu Items */}
                    {item.subItems && (
                      <div className="bg-gray-25 border-l-2 border-gray-200 ml-6">
                        {item.subItems.map((subItem) => (
                          <div
                            key={subItem.code}
                            className={cn(
                              "flex items-center px-6 py-1.5 cursor-pointer text-xs",
                              "hover:bg-blue-50 hover:text-blue-700 transition-colors",
                              "text-gray-600",
                              activeSection === subItem.code &&
                                "bg-blue-100 text-blue-800 font-medium"
                            )}
                            onClick={() => {
                              onSectionChange(subItem.code);
                              setIsOpen(false);
                            }}
                          >
                            <div className="w-1.5 h-1.5 bg-gray-300 rounded-full mr-2"></div>
                            <span className="truncate">{subItem.name}</span>
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                ))}
              </div>
            )}
          </div>
        );
      })}
    </div>
  );

  return (
    <>
      {/* Desktop Navigation - WinForms Style */}
      <nav className="hidden md:block w-72 h-screen bg-white border-r-2 border-gray-300 shadow-lg overflow-y-auto">
        {/* Header */}
        <div className="bg-gradient-to-r from-gray-700 to-gray-800 text-white p-3 border-b-2 border-gray-600">
          <h2 className="font-bold text-sm flex items-center gap-2">
            <Menu className="h-4 w-4" />
            HỆ THỐNG QUẢN LÝ NHÂN SỰ
          </h2>
        </div>

        {/* Navigation Content */}
        <div className="bg-gray-50">
          <NavigationContent />
        </div>
      </nav>

      {/* Mobile Navigation */}
      <Sheet open={isOpen} onOpenChange={setIsOpen}>
        <SheetTrigger asChild>
          <Button
            variant="ghost"
            size="icon"
            className="md:hidden bg-blue-600 hover:bg-blue-700 text-white"
          >
            <Menu className="h-5 w-5" />
          </Button>
        </SheetTrigger>
        <SheetContent side="left" className="w-80 p-0 bg-white">
          {/* Mobile Header */}
          <div className="bg-gradient-to-r from-gray-700 to-gray-800 text-white p-4 border-b-2 border-gray-600">
            <h2 className="font-bold text-sm flex items-center gap-2">
              <Menu className="h-4 w-4" />
              HỆ THỐNG QUẢN LÝ NHÂN SỰ
            </h2>
          </div>

          {/* Mobile Navigation Content */}
          <div className="bg-gray-50 h-full overflow-y-auto">
            <NavigationContent />
          </div>
        </SheetContent>
      </Sheet>
    </>
  );
}
