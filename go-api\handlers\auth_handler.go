package handlers

import (
	"go-api/models"
	"go-api/services"
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
)

// Khởi tạo service (Dependency Injection)
var authService = services.NewAuthService()

// Register - Handler xử lý đăng ký user mới
// POST /api/auth/register
func Register(c *gin.Context) {
	// Bước 1: Parse và validate request body
	var req models.RegisterRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		// Trả về lỗi validation với status 400
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "Dữ liệu đầu vào không hợp lệ",
			"details": err.<PERSON>rror(),
		})
		return
	}

	// Bước 2: Gọi service xử lý logic đăng ký
	userResponse, err := authService.Register(&req)
	if err != nil {
		// Lỗi business logic (user đã tồn tại, etc.)
		c.JSON(http.StatusConflict, gin.H{
			"error": err.<PERSON>(),
		})
		return
	}

	// Bước 3: Tr<PERSON> về response	 thành công với UserResponse
	c.JSON(http.StatusCreated, gin.H{
		"message": "Đăng ký thành công",
		"user":    userResponse, // Chỉ trả về thông tin cần thiết
	})
}

// Login - Handler xử lý đăng nhập với cookie
// POST /api/auth/login
func Login(c *gin.Context) {
	// Bước 1: Parse request body
	var req models.LoginRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "Dữ liệu đăng nhập không hợp lệ",
			"details": err.Error(),
		})
		return
	}

	// Bước 2: Xử lý logic đăng nhập - service trả về thêm refreshToken và expires
	authResponse, refreshToken, expires, err := authService.Login(&req)
	if err != nil {
		// Lỗi authentication (sai password, user không tồn tại)
		c.JSON(http.StatusUnauthorized, gin.H{
			"error": err.Error(),
		})
		return
	}

	// Bước 3: Set refresh token vào HTTP-only cookie (bảo mật cao)
	c.SetCookie(
	"refresh_token",                     // Tên cookie
	refreshToken,                        // Giá trị refresh token
	int(time.Until(expires).Seconds()),  // Thời gian sống (seconds)
	"/",                                 // Path - áp dụng cho toàn site
	"",                                  // Domain - để trống sẽ áp dụng cho current domain
	false,                               // Secure - true nếu dùng HTTPS
	true,                                // HttpOnly - ngăn JS truy cập (chống XSS)
)

	// Bước 4: Trả về access token và thông tin user (không bao gồm refresh token)
	c.JSON(http.StatusOK, gin.H{
		"message": "Đăng nhập thành công",
		"access_token":    authResponse.AccessToken,
		"user" : authResponse.User,
	})
}

// RefreshToken - Handler làm mới access token từ cookie
// POST /api/auth/refresh
func RefreshToken(c *gin.Context) {
	// Bước 1: Lấy refresh token từ cookie thay vì header
	refreshToken, err := c.Cookie("refresh_token")
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Refresh token cookie không tồn tại hoặc đã hết hạn",
		})
		return
	}

	// Bước 2: Xử lý logic refresh token
	authResponse, newRefreshToken, expires, err := authService.RefreshToken(refreshToken)
	if err != nil {
		// Xóa cookie cũ nếu token không hợp lệ
		c.SetCookie("refresh_token", "", -1, "/", "", false, true)
		
		c.JSON(http.StatusUnauthorized, gin.H{
			"error": err.Error(),
		})
		return
	}

	// Bước 3: Set refresh token mới vào cookie
	c.SetCookie(
		"refresh_token",
		newRefreshToken,
		int(time.Until(expires).Seconds()),
		"/",
		"",
		false, // Set true nếu production sử dụng HTTPS
		true,  // HttpOnly để bảo mật
	)

	// Bước 4: Trả về access token mới
	c.JSON(http.StatusOK, gin.H{
		"message": "Làm mới token thành công",
		"access_token":    authResponse.AccessToken,
	})
}

// Logout - Handler đăng xuất (xóa cookie)
// POST /api/auth/logout
func Logout(c *gin.Context) {
	// Lấy refresh token từ cookie
	refreshToken, err := c.Cookie("refresh_token")
	if err == nil && refreshToken != "" {
		// Có thể thêm logic xóa refresh token khỏi database nếu cần
		// authService.RevokeRefreshToken(refreshToken)
	}

	// Xóa refresh token cookie
	c.SetCookie(
		"refresh_token", 
		"",     // Giá trị rỗng
		-1,     // Expire ngay lập tức
		"/", 
		"", 
		false, 
		true,
	)

	c.JSON(http.StatusOK, gin.H{
		"message": "Đăng xuất thành công",
	})
}