package services

import (
	"errors"
	"go-api/models"
	"go-api/repositories"
	"go-api/utils"
	"time"

	"golang.org/x/crypto/bcrypt" // Thư viện hash password
)

// AuthService - Service xử lý authentication
type AuthService struct {
	userRepo *repositories.UserRepository // Dependency injection
}

// NewAuthService - Constructor tạo AuthService
func NewAuthService() *AuthService {
	return &AuthService{
		userRepo: repositories.NewUserRepository(),
	}
}

// Register - Xử lý logic đăng ký user mới
func (s *AuthService) Register(req *models.RegisterRequest) (*models.User, error) {
	// Bước 1: Kiểm tra user đã tồn tại chưa
	existingUser, _ := s.userRepo.GetUserByUserID(req.UserID)
	if existingUser != nil {
		return nil, errors.New("userid đã tồn tại")
	}

	// Bước 2: Hash password để bảo mật
	// Cost 12 = 2^12 iterations, cân bằng giữa security và performance
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(req.Password), 12)
	if err != nil {
		return nil, errors.New("lỗi mã hóa password")
	}

	// Bước 3: Tạo UserAuth object cho database
	userAuth := &models.UserAuth{
		UserID:       req.UserID,
		PasswordHash: string(hashedPassword),
		FullName:     req.FullName,
		Email:        req.Email,
	}

	// Bước 4: Lưu vào database
	err = s.userRepo.CreateUser(userAuth)
	if err != nil {
		return nil, errors.New("lỗi tạo user trong database")
	}

	// Bước 5: Trả về User (không bao gồm thông tin nhạy cảm)
	user := userAuth.ToUser()
	return &user, nil
}

// Login - Xử lý logic đăng nhập với cookie
func (s *AuthService) Login(req *models.LoginRequest) (*models.AuthResponse, string, time.Time, error) {
	// Bước 1: Tìm user theo userid (lấy thông tin authentication)
	userAuth, err := s.userRepo.GetUserAuthByUserID(req.UserID)
	if err != nil {
		return nil, "", time.Time{}, errors.New("thông tin đăng nhập không chính xác")
	}

	// Bước 2: So sánh password với hash đã lưu
	err = bcrypt.CompareHashAndPassword([]byte(userAuth.PasswordHash), []byte(req.Password))
	if err != nil {
		return nil, "", time.Time{}, errors.New("thông tin đăng nhập không chính xác")
	}

	// Bước 3: Tạo access token (ngắn hạn)
	accessToken, err := utils.GenerateAccessToken(userAuth.UserID)
	if err != nil {
		return nil, "", time.Time{}, errors.New("lỗi tạo access token")
	}

	// Bước 4: Tạo refresh token (dài hạn)
	refreshToken, expires, err := utils.GenerateRefreshToken()
	if err != nil {
		return nil, "", time.Time{}, errors.New("lỗi tạo refresh token")
	}

	// Bước 5: Lưu refresh token vào database
	err = s.userRepo.UpdateRefreshToken(userAuth.UserID, refreshToken, expires)
	if err != nil {
		return nil, "", time.Time{}, errors.New("lỗi lưu refresh token")
	}

	// Bước 6: Trả về response với User (chỉ thông tin cần thiết)
	authResponse := &models.AuthResponse{
		AccessToken: accessToken,
		User:        userAuth.ToUser(), // Chuyển đổi UserAuth -> User
	}
	
	// Trả về authResponse, refreshToken để set cookie, và expires time
	return authResponse, refreshToken, expires, nil
}

// RefreshToken - Làm mới access token bằng refresh token từ cookie
func (s *AuthService) RefreshToken(refreshToken string) (*models.AuthResponse, string, time.Time, error) {
	// Bước 1: Validate refresh token từ database
	userAuth, err := s.userRepo.ValidateRefreshToken(refreshToken)
	if err != nil {
		return nil, "", time.Time{}, errors.New("refresh token không hợp lệ hoặc đã hết hạn")
	}

	// Bước 2: Tạo access token mới
	accessToken, err := utils.GenerateAccessToken(userAuth.UserID)
	if err != nil {
		return nil, "", time.Time{}, errors.New("lỗi tạo access token mới")
	}

	// Bước 3: Tạo refresh token mới (rotation strategy - tăng bảo mật)
	newRefreshToken, expires, err := utils.GenerateRefreshToken()
	if err != nil {
		return nil, "", time.Time{}, errors.New("lỗi tạo refresh token mới")
	}

	// Bước 4: Cập nhật refresh token mới vào database
	err = s.userRepo.UpdateRefreshToken(userAuth.UserID, newRefreshToken, expires)
	if err != nil {
		return nil, "", time.Time{}, errors.New("lỗi cập nhật refresh token")
	}

	// Bước 5: Trả về response với User
	authResponse := &models.AuthResponse{
		AccessToken: accessToken,
		User:        userAuth.ToUser(), // Chuyển đổi UserAuth -> User
	}
	
	return authResponse, newRefreshToken, expires, nil
}
