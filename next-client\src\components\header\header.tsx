"use client";

import { Logo } from "./logo";
import { Navigation } from "./navigation";
import { HeaderActions } from "./header-actions";

interface HeaderProps {
  activeSection: string;
  onSectionChange: (section: string) => void;
}

export function Header({ activeSection, onSectionChange }: HeaderProps) {
  return (
    <header className="sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
      <div className="w-full flex h-16 items-center justify-between px-4">
        <Logo />
        <Navigation
          activeSection={activeSection}
          onSectionChange={onSectionChange}
        />
        <HeaderActions />
      </div>
    </header>
  );
}
