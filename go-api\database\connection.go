// database/connection.go - Quản lý kết nối đến SQL Server
package database

import (
	"fmt"
	"log"
	"os"

	_ "github.com/denisenkom/go-mssqldb" // Import driver SQL Server
	"github.com/jmoiron/sqlx"
)

// DB - Biến global chứa kết nối database
var DB *sqlx.DB

// Connect - Thiết lập kết nối đến SQL Server
func Connect() {
	// Đọc thông tin kết nối từ environment variables
	server := os.Getenv("DB_SERVER")     // Địa chỉ server
	port := os.Getenv("DB_PORT")         // Port (mặc định 1433)
	user := os.Getenv("DB_USER")         // Username
	password := os.Getenv("DB_PASSWORD") // Password
	database := os.Getenv("DB_NAME")     // Tên database

	// Thiết lập port mặc định nếu không có
	if port == "" {
		port = "1433"
	}

	// Tạo connection string cho SQL Server
	// encrypt=disable: Tắt mã hóa SSL (chỉ dùng cho development)
	connString := fmt.Sprintf("server=%s;port=%s;user id=%s;password=%s;database=%s;encrypt=disable",
		server, port, user, password, database)

	// Thực hiện kết nối
	var err error
	DB, err = sqlx.Connect("sqlserver", connString)
	if err != nil {
		log.Fatal("❌ Không thể kết nối database:", err)
	}

	// Kiểm tra kết nối bằng cách ping
	if err = DB.Ping(); err != nil {
		log.Fatal("❌ Ping database thất bại:", err)
	}

	log.Println("✅ Kết nối database thành công!")
}