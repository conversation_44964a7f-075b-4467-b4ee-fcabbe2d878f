package utils

import (
	"os"
	"time"

	"github.com/golang-jwt/jwt/v5"
)

// getJWTKey - Lấy JWT secret key từ environment
func getJWTKey() []byte {
	key := os.Getenv("JWT_SECRET")
	if key == "" {
		key = "default-secret-key" // Chỉ dùng cho development
	}
	return []byte(key)
}

// GenerateAccessToken - Tạo access token (thời gian ngắn)
func GenerateAccessToken(userID string) (string, error) {
	// Claims chứa thông tin payload của token
	claims := jwt.MapClaims{
		"sub": userID,                                    // Subject: ID người dùng
		"exp": time.Now().Add(15 * time.Minute).Unix(),   // Expire: Hết hạn sau 15 phút
		"iat": time.Now().Unix(),                         // Issued at: Thời gian tạo
		"type": "access",                                 // Loại token
	}
	
	// Tạo token với thuật toán <PERSON>S256
	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	
	// Ký token với secret key
	return token.SignedString(getJWTKey())
}

// GenerateRefreshToken - Tạo refresh token (thời gian dài)
func GenerateRefreshToken() (string, time.Time, error) {
	// Thời gian hết hạn: 7 ngày
	expiration := time.Now().Add(7 * 24 * time.Hour)
	
	claims := jwt.MapClaims{
		"sub":  "refresh",                    // Subject: refresh token
		"exp":  expiration.Unix(),            // Expire: Hết hạn sau 7 ngày
		"iat":  time.Now().Unix(),            // Issued at: Thời gian tạo
		"rnd":  time.Now().UnixNano(),        // Random: Tăng tính unique
		"type": "refresh",                    // Loại token
	}
	
	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	signed, err := token.SignedString(getJWTKey())
	
	return signed, expiration, err
}

// ValidateToken - Xác thực và parse token
func ValidateToken(tokenStr string) (*jwt.Token, error) {
	// Parse token với key function
	return jwt.Parse(tokenStr, func(token *jwt.Token) (interface{}, error) {
		// Kiểm tra signing method
		if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
			return nil, jwt.ErrSignatureInvalid
		}
		return getJWTKey(), nil
	})
}