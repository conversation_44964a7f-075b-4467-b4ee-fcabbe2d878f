{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Project/Golang/hrm/next-client/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 23, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Project/Golang/hrm/next-client/src/components/ui/separator.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SeparatorPrimitive from \"@radix-ui/react-separator\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Separator({\n  className,\n  orientation = \"horizontal\",\n  decorative = true,\n  ...props\n}: React.ComponentProps<typeof SeparatorPrimitive.Root>) {\n  return (\n    <SeparatorPrimitive.Root\n      data-slot=\"separator\"\n      decorative={decorative}\n      orientation={orientation}\n      className={cn(\n        \"bg-border shrink-0 data-[orientation=horizontal]:h-px data-[orientation=horizontal]:w-full data-[orientation=vertical]:h-full data-[orientation=vertical]:w-px\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Separator }\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,UAAU,EACjB,SAAS,EACT,cAAc,YAAY,EAC1B,aAAa,IAAI,EACjB,GAAG,OACkD;IACrD,qBACE,8OAAC,qKAAA,CAAA,OAAuB;QACtB,aAAU;QACV,YAAY;QACZ,aAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kKACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 53, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Project/Golang/hrm/next-client/src/components/footer/footer.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useEffect, useState } from \"react\";\r\nimport { Separator } from \"@/components/ui/separator\";\r\n\r\ninterface FooterProps {\r\n  activeSection: string;\r\n}\r\n\r\nexport function Footer({ activeSection }: FooterProps) {\r\n  const [currentTime, setCurrentTime] = useState(new Date());\r\n  const [loginTime] = useState(new Date());\r\n  const [sessionDuration, setSessionDuration] = useState(\"00:00:00\");\r\n  const [userIP, setUserIP] = useState(\"Loading...\");\r\n\r\n  // Update current time every second\r\n  useEffect(() => {\r\n    const timer = setInterval(() => {\r\n      setCurrentTime(new Date());\r\n    }, 1000);\r\n\r\n    return () => clearInterval(timer);\r\n  }, []);\r\n\r\n  // Calculate session duration\r\n  useEffect(() => {\r\n    const timer = setInterval(() => {\r\n      const now = new Date();\r\n      const diff = now.getTime() - loginTime.getTime();\r\n      const hours = Math.floor(diff / (1000 * 60 * 60));\r\n      const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));\r\n      const seconds = Math.floor((diff % (1000 * 60)) / 1000);\r\n\r\n      setSessionDuration(\r\n        `${hours.toString().padStart(2, \"0\")}:${minutes\r\n          .toString()\r\n          .padStart(2, \"0\")}:${seconds.toString().padStart(2, \"0\")}`\r\n      );\r\n    }, 1000);\r\n\r\n    return () => clearInterval(timer);\r\n  }, [loginTime]);\r\n\r\n  // Get user IP (mock implementation)\r\n  useEffect(() => {\r\n    // In a real application, you would fetch this from an API\r\n    // For demo purposes, we'll use a mock IP\r\n    setTimeout(() => {\r\n      setUserIP(\"*************\");\r\n    }, 1000);\r\n  }, []);\r\n\r\n  return (\r\n    <footer className=\"border-t bg-background\">\r\n      <div className=\"container mx-auto px-4 py-3\">\r\n        <div className=\"flex flex-col sm:flex-row items-center justify-between gap-2 text-sm text-muted-foreground\">\r\n          <div className=\"flex items-center gap-4\">\r\n            <span className=\"font-medium\">\r\n              Current Section: {activeSection}\r\n            </span>\r\n            <Separator orientation=\"vertical\" className=\"h-4\" />\r\n            <span>IP: {userIP}</span>\r\n          </div>\r\n\r\n          <div className=\"flex items-center gap-4\">\r\n            <span>Login: {loginTime.toLocaleTimeString()}</span>\r\n            <Separator orientation=\"vertical\" className=\"h-4\" />\r\n            <span>Session: {sessionDuration}</span>\r\n            <Separator orientation=\"vertical\" className=\"h-4\" />\r\n            <span>{currentTime.toLocaleString()}</span>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </footer>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AASO,SAAS,OAAO,EAAE,aAAa,EAAe;IACnD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,IAAI;IACnD,MAAM,CAAC,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,IAAI;IACjC,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAErC,mCAAmC;IACnC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,QAAQ,YAAY;YACxB,eAAe,IAAI;QACrB,GAAG;QAEH,OAAO,IAAM,cAAc;IAC7B,GAAG,EAAE;IAEL,6BAA6B;IAC7B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,QAAQ,YAAY;YACxB,MAAM,MAAM,IAAI;YAChB,MAAM,OAAO,IAAI,OAAO,KAAK,UAAU,OAAO;YAC9C,MAAM,QAAQ,KAAK,KAAK,CAAC,OAAO,CAAC,OAAO,KAAK,EAAE;YAC/C,MAAM,UAAU,KAAK,KAAK,CAAC,AAAC,OAAO,CAAC,OAAO,KAAK,EAAE,IAAK,CAAC,OAAO,EAAE;YACjE,MAAM,UAAU,KAAK,KAAK,CAAC,AAAC,OAAO,CAAC,OAAO,EAAE,IAAK;YAElD,mBACE,GAAG,MAAM,QAAQ,GAAG,QAAQ,CAAC,GAAG,KAAK,CAAC,EAAE,QACrC,QAAQ,GACR,QAAQ,CAAC,GAAG,KAAK,CAAC,EAAE,QAAQ,QAAQ,GAAG,QAAQ,CAAC,GAAG,MAAM;QAEhE,GAAG;QAEH,OAAO,IAAM,cAAc;IAC7B,GAAG;QAAC;KAAU;IAEd,oCAAoC;IACpC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,0DAA0D;QAC1D,yCAAyC;QACzC,WAAW;YACT,UAAU;QACZ,GAAG;IACL,GAAG,EAAE;IAEL,qBACE,8OAAC;QAAO,WAAU;kBAChB,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAK,WAAU;;oCAAc;oCACV;;;;;;;0CAEpB,8OAAC,qIAAA,CAAA,YAAS;gCAAC,aAAY;gCAAW,WAAU;;;;;;0CAC5C,8OAAC;;oCAAK;oCAAK;;;;;;;;;;;;;kCAGb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;;oCAAK;oCAAQ,UAAU,kBAAkB;;;;;;;0CAC1C,8OAAC,qIAAA,CAAA,YAAS;gCAAC,aAAY;gCAAW,WAAU;;;;;;0CAC5C,8OAAC;;oCAAK;oCAAU;;;;;;;0CAChB,8OAAC,qIAAA,CAAA,YAAS;gCAAC,aAAY;gCAAW,WAAU;;;;;;0CAC5C,8OAAC;0CAAM,YAAY,cAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAM7C", "debugId": null}}, {"offset": {"line": 217, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Project/Golang/hrm/next-client/src/components/header/logo.tsx"], "sourcesContent": ["import { Building2 } from \"lucide-react\";\r\n\r\nexport function Logo() {\r\n  return (\r\n    <div className=\"flex items-center space-x-2\">\r\n      <Building2 className=\"h-8 w-8 text-primary\" />\r\n      <span className=\"hidden font-bold sm:inline-block text-xl\">\r\n        Dashboard\r\n      </span>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;;;AAEO,SAAS;IACd,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,gNAAA,CAAA,YAAS;gBAAC,WAAU;;;;;;0BACrB,8OAAC;gBAAK,WAAU;0BAA2C;;;;;;;;;;;;AAKjE", "debugId": null}}, {"offset": {"line": 256, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Project/Golang/hrm/next-client/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\n        secondary:\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\n        ghost:\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\n        icon: \"size-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nfunction Button({\n  className,\n  variant,\n  size,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"button\"> &\n  VariantProps<typeof buttonVariants> & {\n    asChild?: boolean\n  }) {\n  const Comp = asChild ? Slot : \"button\"\n\n  return (\n    <Comp\n      data-slot=\"button\"\n      className={cn(buttonVariants({ variant, size, className }))}\n      {...props}\n    />\n  )\n}\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 313, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Project/Golang/hrm/next-client/src/components/ui/sheet.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SheetPrimitive from \"@radix-ui/react-dialog\"\nimport { XIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Sheet({ ...props }: React.ComponentProps<typeof SheetPrimitive.Root>) {\n  return <SheetPrimitive.Root data-slot=\"sheet\" {...props} />\n}\n\nfunction SheetTrigger({\n  ...props\n}: React.ComponentProps<typeof SheetPrimitive.Trigger>) {\n  return <SheetPrimitive.Trigger data-slot=\"sheet-trigger\" {...props} />\n}\n\nfunction SheetClose({\n  ...props\n}: React.ComponentProps<typeof SheetPrimitive.Close>) {\n  return <SheetPrimitive.Close data-slot=\"sheet-close\" {...props} />\n}\n\nfunction SheetPortal({\n  ...props\n}: React.ComponentProps<typeof SheetPrimitive.Portal>) {\n  return <SheetPrimitive.Portal data-slot=\"sheet-portal\" {...props} />\n}\n\nfunction SheetOverlay({\n  className,\n  ...props\n}: React.ComponentProps<typeof SheetPrimitive.Overlay>) {\n  return (\n    <SheetPrimitive.Overlay\n      data-slot=\"sheet-overlay\"\n      className={cn(\n        \"data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction SheetContent({\n  className,\n  children,\n  side = \"right\",\n  ...props\n}: React.ComponentProps<typeof SheetPrimitive.Content> & {\n  side?: \"top\" | \"right\" | \"bottom\" | \"left\"\n}) {\n  return (\n    <SheetPortal>\n      <SheetOverlay />\n      <SheetPrimitive.Content\n        data-slot=\"sheet-content\"\n        className={cn(\n          \"bg-background data-[state=open]:animate-in data-[state=closed]:animate-out fixed z-50 flex flex-col gap-4 shadow-lg transition ease-in-out data-[state=closed]:duration-300 data-[state=open]:duration-500\",\n          side === \"right\" &&\n            \"data-[state=closed]:slide-out-to-right data-[state=open]:slide-in-from-right inset-y-0 right-0 h-full w-3/4 border-l sm:max-w-sm\",\n          side === \"left\" &&\n            \"data-[state=closed]:slide-out-to-left data-[state=open]:slide-in-from-left inset-y-0 left-0 h-full w-3/4 border-r sm:max-w-sm\",\n          side === \"top\" &&\n            \"data-[state=closed]:slide-out-to-top data-[state=open]:slide-in-from-top inset-x-0 top-0 h-auto border-b\",\n          side === \"bottom\" &&\n            \"data-[state=closed]:slide-out-to-bottom data-[state=open]:slide-in-from-bottom inset-x-0 bottom-0 h-auto border-t\",\n          className\n        )}\n        {...props}\n      >\n        {children}\n        <SheetPrimitive.Close className=\"ring-offset-background focus:ring-ring data-[state=open]:bg-secondary absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none\">\n          <XIcon className=\"size-4\" />\n          <span className=\"sr-only\">Close</span>\n        </SheetPrimitive.Close>\n      </SheetPrimitive.Content>\n    </SheetPortal>\n  )\n}\n\nfunction SheetHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"sheet-header\"\n      className={cn(\"flex flex-col gap-1.5 p-4\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SheetFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"sheet-footer\"\n      className={cn(\"mt-auto flex flex-col gap-2 p-4\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SheetTitle({\n  className,\n  ...props\n}: React.ComponentProps<typeof SheetPrimitive.Title>) {\n  return (\n    <SheetPrimitive.Title\n      data-slot=\"sheet-title\"\n      className={cn(\"text-foreground font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SheetDescription({\n  className,\n  ...props\n}: React.ComponentProps<typeof SheetPrimitive.Description>) {\n  return (\n    <SheetPrimitive.Description\n      data-slot=\"sheet-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Sheet,\n  SheetTrigger,\n  SheetClose,\n  SheetContent,\n  SheetHeader,\n  SheetFooter,\n  SheetTitle,\n  SheetDescription,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;AAGA;AACA;AAEA;AANA;;;;;AAQA,SAAS,MAAM,EAAE,GAAG,OAAyD;IAC3E,qBAAO,8OAAC,kKAAA,CAAA,OAAmB;QAAC,aAAU;QAAS,GAAG,KAAK;;;;;;AACzD;AAEA,SAAS,aAAa,EACpB,GAAG,OACiD;IACpD,qBAAO,8OAAC,kKAAA,CAAA,UAAsB;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACpE;AAEA,SAAS,WAAW,EAClB,GAAG,OAC+C;IAClD,qBAAO,8OAAC,kKAAA,CAAA,QAAoB;QAAC,aAAU;QAAe,GAAG,KAAK;;;;;;AAChE;AAEA,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,8OAAC,kKAAA,CAAA,SAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;AAEA,SAAS,aAAa,EACpB,SAAS,EACT,GAAG,OACiD;IACpD,qBACE,8OAAC,kKAAA,CAAA,UAAsB;QACrB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,aAAa,EACpB,SAAS,EACT,QAAQ,EACR,OAAO,OAAO,EACd,GAAG,OAGJ;IACC,qBACE,8OAAC;;0BACC,8OAAC;;;;;0BACD,8OAAC,kKAAA,CAAA,UAAsB;gBACrB,aAAU;gBACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8MACA,SAAS,WACP,oIACF,SAAS,UACP,iIACF,SAAS,SACP,4GACF,SAAS,YACP,qHACF;gBAED,GAAG,KAAK;;oBAER;kCACD,8OAAC,kKAAA,CAAA,QAAoB;wBAAC,WAAU;;0CAC9B,8OAAC,gMAAA,CAAA,QAAK;gCAAC,WAAU;;;;;;0CACjB,8OAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;;;;;;;;AAKpC;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,6BAA6B;QAC1C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,mCAAmC;QAChD,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAClB,SAAS,EACT,GAAG,OAC+C;IAClD,qBACE,8OAAC,kKAAA,CAAA,QAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,iBAAiB,EACxB,SAAS,EACT,GAAG,OACqD;IACxD,qBACE,8OAAC,kKAAA,CAAA,cAA0B;QACzB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 485, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Project/Golang/hrm/next-client/src/components/header/navigation.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { cn } from \"@/lib/utils\";\r\nimport { Button } from \"@/components/ui/button\";\r\nimport {\r\n  Settings,\r\n  Users,\r\n  Clock,\r\n  Calculator,\r\n  FileText,\r\n  BarChart3,\r\n  BookOpen,\r\n  Briefcase,\r\n  Heart,\r\n  Menu,\r\n  ChevronDown,\r\n  ChevronRight,\r\n} from \"lucide-react\";\r\nimport { Sheet, SheetContent, SheetTrigger } from \"@/components/ui/sheet\";\r\nimport { useState } from \"react\";\r\nimport {\r\n  Collapsible,\r\n  CollapsibleContent,\r\n  CollapsibleTrigger,\r\n} from \"@/components/ui/collapsible\";\r\n\r\ninterface NavigationProps {\r\n  activeSection: string;\r\n  onSectionChange: (section: string) => void;\r\n}\r\nconst navigationItems = [\r\n  {\r\n    name: \"N1 HỆ THỐNG\",\r\n    code: \"N1\",\r\n    items: [\r\n      { name: \"N11 C<PERSON>u <PERSON>nh <PERSON>\", code: \"N11\" },\r\n      { name: \"N12 Quản Lý Người Dùng\", code: \"N12\" },\r\n      { name: \"N13 Phân Quyền\", code: \"N13\" },\r\n      { name: \"N14 <PERSON><PERSON>\", code: \"N14\" },\r\n    ],\r\n  },\r\n  {\r\n    name: \"N2 DANH MỤC\",\r\n    code: \"N2\",\r\n    items: [\r\n      { name: \"N21 Danh Mục Phòng Ban\", code: \"N21\" },\r\n      { name: \"N22 Danh Mục Chức Vụ\", code: \"N22\" },\r\n      { name: \"N23 Danh Mục Ca Làm\", code: \"N23\" },\r\n      { name: \"N24 Danh Mục Loại Nghỉ\", code: \"N24\" },\r\n    ],\r\n  },\r\n  {\r\n    name: \"N3 NHÂN SỰ\",\r\n    code: \"N3\",\r\n    items: [\r\n      {\r\n        name: \"N31 Điểm Danh Hàng Ngày\",\r\n        code: \"N31\",\r\n        subItems: [\r\n          { name: \"N311 Điểm Danh\", code: \"N311\" },\r\n          { name: \"N312 Điều Động Hàng Tháng\", code: \"N312\" },\r\n        ],\r\n      },\r\n      { name: \"N32 Nhân Sự\", code: \"N32\" },\r\n      { name: \"N33 Hợp Đồng\", code: \"N33\" },\r\n      { name: \"N34 Bình Bầu\", code: \"N34\" },\r\n      { name: \"N35 Nghỉ Phép\", code: \"N35\" },\r\n      { name: \"N36 Giải Quyết Thôi Việc\", code: \"N36\" },\r\n      { name: \"N37 Ký Luật Lao Động\", code: \"N37\" },\r\n      { name: \"N38 Cập Nhật Nhà Xưởng\", code: \"N38\" },\r\n      { name: \"N39 Lý Lịch Nhân Viên\", code: \"N39\" },\r\n      { name: \"N3A Chuyển Đơn Vị\", code: \"N3A\" },\r\n      { name: \"N3B Nơi Đăng Ký Khám Chữa Bệnh\", code: \"N3B\" },\r\n      { name: \"N3C Chuyển Công Việc\", code: \"N3C\" },\r\n      { name: \"N3D Danh Sách Cát Lượng NNDH\", code: \"N3D\" },\r\n      { name: \"N3E Đề Nghị Làm Lại Thẻ Từ\", code: \"N3E\" },\r\n      { name: \"N3G Theo dõi Lương Bình bầu CNV Thôi Việc\", code: \"N3G\" },\r\n      { name: \"N3H Thống Kê KPI\", code: \"N3H\" },\r\n      { name: \"N3K Sổ Quản Lý Lao Động\", code: \"N3K\" },\r\n      { name: \"N3L Tài Nạn Lao Động\", code: \"N3L\" },\r\n    ],\r\n  },\r\n  {\r\n    name: \"N4 CHẤM CÔNG\",\r\n    code: \"N4\",\r\n    items: [\r\n      { name: \"N41 Chấm Công Hàng Ngày\", code: \"N41\" },\r\n      { name: \"N42 Tổng Hợp Chấm Công\", code: \"N42\" },\r\n      { name: \"N43 Báo Cáo Chấm Công\", code: \"N43\" },\r\n    ],\r\n  },\r\n  {\r\n    name: \"N5 TÍNH LƯƠNG\",\r\n    code: \"N5\",\r\n    items: [\r\n      { name: \"N51 Bảng Lương Cơ Bản\", code: \"N51\" },\r\n      { name: \"N52 Tính Lương Tháng\", code: \"N52\" },\r\n      { name: \"N53 Phụ Cấp\", code: \"N53\" },\r\n      { name: \"N54 Thưởng Phạt\", code: \"N54\" },\r\n    ],\r\n  },\r\n  {\r\n    name: \"N6 BÁO CÁO\",\r\n    code: \"N6\",\r\n    items: [\r\n      { name: \"N61 Báo Cáo Nhân Sự\", code: \"N61\" },\r\n      { name: \"N62 Báo Cáo Chấm Công\", code: \"N62\" },\r\n      { name: \"N63 Báo Cáo Lương\", code: \"N63\" },\r\n      { name: \"N64 Báo Cáo Tổng Hợp\", code: \"N64\" },\r\n    ],\r\n  },\r\n  {\r\n    name: \"N7 TÀI LIỆU\",\r\n    code: \"N7\",\r\n    items: [\r\n      { name: \"N71 Hướng Dẫn Sử Dụng\", code: \"N71\" },\r\n      { name: \"N72 Quy Trình Nghiệp Vụ\", code: \"N72\" },\r\n      { name: \"N73 Biểu Mẫu\", code: \"N73\" },\r\n    ],\r\n  },\r\n  {\r\n    name: \"N8 QUẢN LÝ\",\r\n    code: \"N8\",\r\n    items: [\r\n      { name: \"N81 Quản Lý Dự Án\", code: \"N81\" },\r\n      { name: \"N82 Quản Lý Tài Sản\", code: \"N82\" },\r\n      { name: \"N83 Quản Lý Kho\", code: \"N83\" },\r\n    ],\r\n  },\r\n  {\r\n    name: \"N9 Y TẾ\",\r\n    code: \"N9\",\r\n    items: [\r\n      { name: \"N91 Khám Sức Khỏe\", code: \"N91\" },\r\n      { name: \"N92 Theo Dõi Sức Khỏe\", code: \"N92\" },\r\n      { name: \"N93 Báo Cáo Y Tế\", code: \"N93\" },\r\n    ],\r\n  },\r\n];\r\n\r\nexport function Navigation({\r\n  activeSection,\r\n  onSectionChange,\r\n}: NavigationProps) {\r\n  const [isOpen, setIsOpen] = useState(false);\r\n\r\n  const NavigationContent = () => (\r\n    <>\r\n      {navigationItems.map((item) => {\r\n        return (\r\n          <Button\r\n            key={item.name}\r\n            variant={activeSection === item.name ? \"default\" : \"ghost\"}\r\n            className={cn(\r\n              \"justify-start gap-2\",\r\n              activeSection === item.name &&\r\n                \"bg-primary text-primary-foreground\"\r\n            )}\r\n            onClick={() => {\r\n              onSectionChange(item.name);\r\n              setIsOpen(false);\r\n            }}\r\n          >\r\n            <span className=\"hidden sm:inline\">{item.name}</span>\r\n          </Button>\r\n        );\r\n      })}\r\n    </>\r\n  );\r\n\r\n  return (\r\n    <>\r\n      {/* Desktop Navigation */}\r\n      <nav className=\"hidden md:flex items-center space-x-2\">\r\n        <NavigationContent />\r\n      </nav>\r\n\r\n      {/* Mobile Navigation */}\r\n      <Sheet open={isOpen} onOpenChange={setIsOpen}>\r\n        <SheetTrigger asChild>\r\n          <Button variant=\"ghost\" size=\"icon\" className=\"md:hidden\">\r\n            <Menu className=\"h-5 w-5\" />\r\n          </Button>\r\n        </SheetTrigger>\r\n        <SheetContent side=\"left\" className=\"w-64\">\r\n          <div className=\"flex flex-col space-y-2 mt-8\">\r\n            <NavigationContent />\r\n          </div>\r\n        </SheetContent>\r\n      </Sheet>\r\n    </>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAcA;AACA;AAnBA;;;;;;;AA8BA,MAAM,kBAAkB;IACtB;QACE,MAAM;QACN,MAAM;QACN,OAAO;YACL;gBAAE,MAAM;gBAAyB,MAAM;YAAM;YAC7C;gBAAE,MAAM;gBAA0B,MAAM;YAAM;YAC9C;gBAAE,MAAM;gBAAkB,MAAM;YAAM;YACtC;gBAAE,MAAM;gBAAuB,MAAM;YAAM;SAC5C;IACH;IACA;QACE,MAAM;QACN,MAAM;QACN,OAAO;YACL;gBAAE,MAAM;gBAA0B,MAAM;YAAM;YAC9C;gBAAE,MAAM;gBAAwB,MAAM;YAAM;YAC5C;gBAAE,MAAM;gBAAuB,MAAM;YAAM;YAC3C;gBAAE,MAAM;gBAA0B,MAAM;YAAM;SAC/C;IACH;IACA;QACE,MAAM;QACN,MAAM;QACN,OAAO;YACL;gBACE,MAAM;gBACN,MAAM;gBACN,UAAU;oBACR;wBAAE,MAAM;wBAAkB,MAAM;oBAAO;oBACvC;wBAAE,MAAM;wBAA6B,MAAM;oBAAO;iBACnD;YACH;YACA;gBAAE,MAAM;gBAAe,MAAM;YAAM;YACnC;gBAAE,MAAM;gBAAgB,MAAM;YAAM;YACpC;gBAAE,MAAM;gBAAgB,MAAM;YAAM;YACpC;gBAAE,MAAM;gBAAiB,MAAM;YAAM;YACrC;gBAAE,MAAM;gBAA4B,MAAM;YAAM;YAChD;gBAAE,MAAM;gBAAwB,MAAM;YAAM;YAC5C;gBAAE,MAAM;gBAA0B,MAAM;YAAM;YAC9C;gBAAE,MAAM;gBAAyB,MAAM;YAAM;YAC7C;gBAAE,MAAM;gBAAqB,MAAM;YAAM;YACzC;gBAAE,MAAM;gBAAkC,MAAM;YAAM;YACtD;gBAAE,MAAM;gBAAwB,MAAM;YAAM;YAC5C;gBAAE,MAAM;gBAAgC,MAAM;YAAM;YACpD;gBAAE,MAAM;gBAA8B,MAAM;YAAM;YAClD;gBAAE,MAAM;gBAA6C,MAAM;YAAM;YACjE;gBAAE,MAAM;gBAAoB,MAAM;YAAM;YACxC;gBAAE,MAAM;gBAA2B,MAAM;YAAM;YAC/C;gBAAE,MAAM;gBAAwB,MAAM;YAAM;SAC7C;IACH;IACA;QACE,MAAM;QACN,MAAM;QACN,OAAO;YACL;gBAAE,MAAM;gBAA2B,MAAM;YAAM;YAC/C;gBAAE,MAAM;gBAA0B,MAAM;YAAM;YAC9C;gBAAE,MAAM;gBAAyB,MAAM;YAAM;SAC9C;IACH;IACA;QACE,MAAM;QACN,MAAM;QACN,OAAO;YACL;gBAAE,MAAM;gBAAyB,MAAM;YAAM;YAC7C;gBAAE,MAAM;gBAAwB,MAAM;YAAM;YAC5C;gBAAE,MAAM;gBAAe,MAAM;YAAM;YACnC;gBAAE,MAAM;gBAAmB,MAAM;YAAM;SACxC;IACH;IACA;QACE,MAAM;QACN,MAAM;QACN,OAAO;YACL;gBAAE,MAAM;gBAAuB,MAAM;YAAM;YAC3C;gBAAE,MAAM;gBAAyB,MAAM;YAAM;YAC7C;gBAAE,MAAM;gBAAqB,MAAM;YAAM;YACzC;gBAAE,MAAM;gBAAwB,MAAM;YAAM;SAC7C;IACH;IACA;QACE,MAAM;QACN,MAAM;QACN,OAAO;YACL;gBAAE,MAAM;gBAAyB,MAAM;YAAM;YAC7C;gBAAE,MAAM;gBAA2B,MAAM;YAAM;YAC/C;gBAAE,MAAM;gBAAgB,MAAM;YAAM;SACrC;IACH;IACA;QACE,MAAM;QACN,MAAM;QACN,OAAO;YACL;gBAAE,MAAM;gBAAqB,MAAM;YAAM;YACzC;gBAAE,MAAM;gBAAuB,MAAM;YAAM;YAC3C;gBAAE,MAAM;gBAAmB,MAAM;YAAM;SACxC;IACH;IACA;QACE,MAAM;QACN,MAAM;QACN,OAAO;YACL;gBAAE,MAAM;gBAAqB,MAAM;YAAM;YACzC;gBAAE,MAAM;gBAAyB,MAAM;YAAM;YAC7C;gBAAE,MAAM;gBAAoB,MAAM;YAAM;SACzC;IACH;CACD;AAEM,SAAS,WAAW,EACzB,aAAa,EACb,eAAe,EACC;IAChB,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAErC,MAAM,oBAAoB,kBACxB;sBACG,gBAAgB,GAAG,CAAC,CAAC;gBACpB,qBACE,8OAAC,kIAAA,CAAA,SAAM;oBAEL,SAAS,kBAAkB,KAAK,IAAI,GAAG,YAAY;oBACnD,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,uBACA,kBAAkB,KAAK,IAAI,IACzB;oBAEJ,SAAS;wBACP,gBAAgB,KAAK,IAAI;wBACzB,UAAU;oBACZ;8BAEA,cAAA,8OAAC;wBAAK,WAAU;kCAAoB,KAAK,IAAI;;;;;;mBAZxC,KAAK,IAAI;;;;;YAepB;;IAIJ,qBACE;;0BAEE,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;;;;;;;;;;0BAIH,8OAAC,iIAAA,CAAA,QAAK;gBAAC,MAAM;gBAAQ,cAAc;;kCACjC,8OAAC,iIAAA,CAAA,eAAY;wBAAC,OAAO;kCACnB,cAAA,8OAAC,kIAAA,CAAA,SAAM;4BAAC,SAAQ;4BAAQ,MAAK;4BAAO,WAAU;sCAC5C,cAAA,8OAAC,kMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;;;;;;;;;;;kCAGpB,8OAAC,iIAAA,CAAA,eAAY;wBAAC,MAAK;wBAAO,WAAU;kCAClC,cAAA,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;;;;;;;;;;;;;;;;;;;;;;;AAMb", "debugId": null}}, {"offset": {"line": 853, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Project/Golang/hrm/next-client/src/components/header/dark-mode-toggle.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { <PERSON>, <PERSON> } from \"lucide-react\";\r\nimport { useTheme } from \"next-themes\";\r\nimport { Button } from \"@/components/ui/button\";\r\nimport { useEffect, useState } from \"react\";\r\n\r\nexport function DarkModeToggle() {\r\n  const { theme, setTheme } = useTheme();\r\n  const [mounted, setMounted] = useState(false);\r\n\r\n  useEffect(() => {\r\n    setMounted(true);\r\n  }, []);\r\n\r\n  if (!mounted) {\r\n    return (\r\n      <Button variant=\"ghost\" size=\"icon\">\r\n        <Sun className=\"h-4 w-4\" />\r\n      </Button>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <Button\r\n      variant=\"ghost\"\r\n      size=\"icon\"\r\n      onClick={() => setTheme(theme === \"light\" ? \"dark\" : \"light\")}\r\n    >\r\n      {theme === \"light\" ? (\r\n        <Moon className=\"h-4 w-4\" />\r\n      ) : (\r\n        <Sun className=\"h-4 w-4\" />\r\n      )}\r\n      <span className=\"sr-only\">Toggle theme</span>\r\n    </Button>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AACA;AACA;AACA;AALA;;;;;;AAOO,SAAS;IACd,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,gJAAA,CAAA,WAAQ,AAAD;IACnC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,WAAW;IACb,GAAG,EAAE;IAEL,IAAI,CAAC,SAAS;QACZ,qBACE,8OAAC,kIAAA,CAAA,SAAM;YAAC,SAAQ;YAAQ,MAAK;sBAC3B,cAAA,8OAAC,gMAAA,CAAA,MAAG;gBAAC,WAAU;;;;;;;;;;;IAGrB;IAEA,qBACE,8OAAC,kIAAA,CAAA,SAAM;QACL,SAAQ;QACR,MAAK;QACL,SAAS,IAAM,SAAS,UAAU,UAAU,SAAS;;YAEpD,UAAU,wBACT,8OAAC,kMAAA,CAAA,OAAI;gBAAC,WAAU;;;;;qCAEhB,8OAAC,gMAAA,CAAA,MAAG;gBAAC,WAAU;;;;;;0BAEjB,8OAAC;gBAAK,WAAU;0BAAU;;;;;;;;;;;;AAGhC", "debugId": null}}, {"offset": {"line": 930, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Project/Golang/hrm/next-client/src/components/ui/dropdown-menu.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as DropdownMenuPrimitive from \"@radix-ui/react-dropdown-menu\"\nimport { CheckIcon, ChevronRightIcon, CircleIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction DropdownMenu({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Root>) {\n  return <DropdownMenuPrimitive.Root data-slot=\"dropdown-menu\" {...props} />\n}\n\nfunction DropdownMenuPortal({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Portal>) {\n  return (\n    <DropdownMenuPrimitive.Portal data-slot=\"dropdown-menu-portal\" {...props} />\n  )\n}\n\nfunction DropdownMenuTrigger({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Trigger>) {\n  return (\n    <DropdownMenuPrimitive.Trigger\n      data-slot=\"dropdown-menu-trigger\"\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuContent({\n  className,\n  sideOffset = 4,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Content>) {\n  return (\n    <DropdownMenuPrimitive.Portal>\n      <DropdownMenuPrimitive.Content\n        data-slot=\"dropdown-menu-content\"\n        sideOffset={sideOffset}\n        className={cn(\n          \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 max-h-(--radix-dropdown-menu-content-available-height) min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border p-1 shadow-md\",\n          className\n        )}\n        {...props}\n      />\n    </DropdownMenuPrimitive.Portal>\n  )\n}\n\nfunction DropdownMenuGroup({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Group>) {\n  return (\n    <DropdownMenuPrimitive.Group data-slot=\"dropdown-menu-group\" {...props} />\n  )\n}\n\nfunction DropdownMenuItem({\n  className,\n  inset,\n  variant = \"default\",\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Item> & {\n  inset?: boolean\n  variant?: \"default\" | \"destructive\"\n}) {\n  return (\n    <DropdownMenuPrimitive.Item\n      data-slot=\"dropdown-menu-item\"\n      data-inset={inset}\n      data-variant={variant}\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground data-[variant=destructive]:text-destructive data-[variant=destructive]:focus:bg-destructive/10 dark:data-[variant=destructive]:focus:bg-destructive/20 data-[variant=destructive]:focus:text-destructive data-[variant=destructive]:*:[svg]:!text-destructive [&_svg:not([class*='text-'])]:text-muted-foreground relative flex cursor-default items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 data-[inset]:pl-8 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuCheckboxItem({\n  className,\n  children,\n  checked,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.CheckboxItem>) {\n  return (\n    <DropdownMenuPrimitive.CheckboxItem\n      data-slot=\"dropdown-menu-checkbox-item\"\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center gap-2 rounded-sm py-1.5 pr-2 pl-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      checked={checked}\n      {...props}\n    >\n      <span className=\"pointer-events-none absolute left-2 flex size-3.5 items-center justify-center\">\n        <DropdownMenuPrimitive.ItemIndicator>\n          <CheckIcon className=\"size-4\" />\n        </DropdownMenuPrimitive.ItemIndicator>\n      </span>\n      {children}\n    </DropdownMenuPrimitive.CheckboxItem>\n  )\n}\n\nfunction DropdownMenuRadioGroup({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.RadioGroup>) {\n  return (\n    <DropdownMenuPrimitive.RadioGroup\n      data-slot=\"dropdown-menu-radio-group\"\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuRadioItem({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.RadioItem>) {\n  return (\n    <DropdownMenuPrimitive.RadioItem\n      data-slot=\"dropdown-menu-radio-item\"\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center gap-2 rounded-sm py-1.5 pr-2 pl-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    >\n      <span className=\"pointer-events-none absolute left-2 flex size-3.5 items-center justify-center\">\n        <DropdownMenuPrimitive.ItemIndicator>\n          <CircleIcon className=\"size-2 fill-current\" />\n        </DropdownMenuPrimitive.ItemIndicator>\n      </span>\n      {children}\n    </DropdownMenuPrimitive.RadioItem>\n  )\n}\n\nfunction DropdownMenuLabel({\n  className,\n  inset,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Label> & {\n  inset?: boolean\n}) {\n  return (\n    <DropdownMenuPrimitive.Label\n      data-slot=\"dropdown-menu-label\"\n      data-inset={inset}\n      className={cn(\n        \"px-2 py-1.5 text-sm font-medium data-[inset]:pl-8\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuSeparator({\n  className,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Separator>) {\n  return (\n    <DropdownMenuPrimitive.Separator\n      data-slot=\"dropdown-menu-separator\"\n      className={cn(\"bg-border -mx-1 my-1 h-px\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuShortcut({\n  className,\n  ...props\n}: React.ComponentProps<\"span\">) {\n  return (\n    <span\n      data-slot=\"dropdown-menu-shortcut\"\n      className={cn(\n        \"text-muted-foreground ml-auto text-xs tracking-widest\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuSub({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Sub>) {\n  return <DropdownMenuPrimitive.Sub data-slot=\"dropdown-menu-sub\" {...props} />\n}\n\nfunction DropdownMenuSubTrigger({\n  className,\n  inset,\n  children,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.SubTrigger> & {\n  inset?: boolean\n}) {\n  return (\n    <DropdownMenuPrimitive.SubTrigger\n      data-slot=\"dropdown-menu-sub-trigger\"\n      data-inset={inset}\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground data-[state=open]:bg-accent data-[state=open]:text-accent-foreground flex cursor-default items-center rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[inset]:pl-8\",\n        className\n      )}\n      {...props}\n    >\n      {children}\n      <ChevronRightIcon className=\"ml-auto size-4\" />\n    </DropdownMenuPrimitive.SubTrigger>\n  )\n}\n\nfunction DropdownMenuSubContent({\n  className,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.SubContent>) {\n  return (\n    <DropdownMenuPrimitive.SubContent\n      data-slot=\"dropdown-menu-sub-content\"\n      className={cn(\n        \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-hidden rounded-md border p-1 shadow-lg\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport {\n  DropdownMenu,\n  DropdownMenuPortal,\n  DropdownMenuTrigger,\n  DropdownMenuContent,\n  DropdownMenuGroup,\n  DropdownMenuLabel,\n  DropdownMenuItem,\n  DropdownMenuCheckboxItem,\n  DropdownMenuRadioGroup,\n  DropdownMenuRadioItem,\n  DropdownMenuSeparator,\n  DropdownMenuShortcut,\n  DropdownMenuSub,\n  DropdownMenuSubTrigger,\n  DropdownMenuSubContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;AAGA;AACA;AAAA;AAAA;AAEA;AANA;;;;;AAQA,SAAS,aAAa,EACpB,GAAG,OACqD;IACxD,qBAAO,8OAAC,4KAAA,CAAA,OAA0B;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACxE;AAEA,SAAS,mBAAmB,EAC1B,GAAG,OACuD;IAC1D,qBACE,8OAAC,4KAAA,CAAA,SAA4B;QAAC,aAAU;QAAwB,GAAG,KAAK;;;;;;AAE5E;AAEA,SAAS,oBAAoB,EAC3B,GAAG,OACwD;IAC3D,qBACE,8OAAC,4KAAA,CAAA,UAA6B;QAC5B,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,oBAAoB,EAC3B,SAAS,EACT,aAAa,CAAC,EACd,GAAG,OACwD;IAC3D,qBACE,8OAAC,4KAAA,CAAA,SAA4B;kBAC3B,cAAA,8OAAC,4KAAA,CAAA,UAA6B;YAC5B,aAAU;YACV,YAAY;YACZ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0jBACA;YAED,GAAG,KAAK;;;;;;;;;;;AAIjB;AAEA,SAAS,kBAAkB,EACzB,GAAG,OACsD;IACzD,qBACE,8OAAC,4KAAA,CAAA,QAA2B;QAAC,aAAU;QAAuB,GAAG,KAAK;;;;;;AAE1E;AAEA,SAAS,iBAAiB,EACxB,SAAS,EACT,KAAK,EACL,UAAU,SAAS,EACnB,GAAG,OAIJ;IACC,qBACE,8OAAC,4KAAA,CAAA,OAA0B;QACzB,aAAU;QACV,cAAY;QACZ,gBAAc;QACd,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,+mBACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,yBAAyB,EAChC,SAAS,EACT,QAAQ,EACR,OAAO,EACP,GAAG,OAC6D;IAChE,qBACE,8OAAC,4KAAA,CAAA,eAAkC;QACjC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,gTACA;QAEF,SAAS;QACR,GAAG,KAAK;;0BAET,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC,4KAAA,CAAA,gBAAmC;8BAClC,cAAA,8OAAC,wMAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGxB;;;;;;;AAGP;AAEA,SAAS,uBAAuB,EAC9B,GAAG,OAC2D;IAC9D,qBACE,8OAAC,4KAAA,CAAA,aAAgC;QAC/B,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,sBAAsB,EAC7B,SAAS,EACT,QAAQ,EACR,GAAG,OAC0D;IAC7D,qBACE,8OAAC,4KAAA,CAAA,YAA+B;QAC9B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,gTACA;QAED,GAAG,KAAK;;0BAET,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC,4KAAA,CAAA,gBAAmC;8BAClC,cAAA,8OAAC,0MAAA,CAAA,aAAU;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGzB;;;;;;;AAGP;AAEA,SAAS,kBAAkB,EACzB,SAAS,EACT,KAAK,EACL,GAAG,OAGJ;IACC,qBACE,8OAAC,4KAAA,CAAA,QAA2B;QAC1B,aAAU;QACV,cAAY;QACZ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qDACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,sBAAsB,EAC7B,SAAS,EACT,GAAG,OAC0D;IAC7D,qBACE,8OAAC,4KAAA,CAAA,YAA+B;QAC9B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,6BAA6B;QAC1C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,qBAAqB,EAC5B,SAAS,EACT,GAAG,OAC0B;IAC7B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,yDACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EACvB,GAAG,OACoD;IACvD,qBAAO,8OAAC,4KAAA,CAAA,MAAyB;QAAC,aAAU;QAAqB,GAAG,KAAK;;;;;;AAC3E;AAEA,SAAS,uBAAuB,EAC9B,SAAS,EACT,KAAK,EACL,QAAQ,EACR,GAAG,OAGJ;IACC,qBACE,8OAAC,4KAAA,CAAA,aAAgC;QAC/B,aAAU;QACV,cAAY;QACZ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kOACA;QAED,GAAG,KAAK;;YAER;0BACD,8OAAC,0NAAA,CAAA,mBAAgB;gBAAC,WAAU;;;;;;;;;;;;AAGlC;AAEA,SAAS,uBAAuB,EAC9B,SAAS,EACT,GAAG,OAC2D;IAC9D,qBACE,8OAAC,4KAAA,CAAA,aAAgC;QAC/B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,ifACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 1192, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Project/Golang/hrm/next-client/src/components/header/language-selector.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useState } from \"react\";\r\nimport { Languages } from \"lucide-react\";\r\nimport { Button } from \"@/components/ui/button\";\r\nimport {\r\n  DropdownMenu,\r\n  DropdownMenuContent,\r\n  DropdownMenuItem,\r\n  DropdownMenuTrigger,\r\n} from \"@/components/ui/dropdown-menu\";\r\n\r\nconst languages = [\r\n  { code: \"vi\", name: \"Tiếng Việt\", flag: \"🇻🇳\" },\r\n  { code: \"en\", name: \"English\", flag: \"🇺🇸\" },\r\n  { code: \"ja\", name: \"日本語\", flag: \"🇯🇵\" },\r\n  { code: \"ko\", name: \"한국어\", flag: \"🇰🇷\" },\r\n];\r\n\r\nexport function LanguageSelector() {\r\n  const [selectedLanguage, setSelectedLanguage] = useState(languages[0]);\r\n\r\n  return (\r\n    <DropdownMenu>\r\n      <DropdownMenuTrigger asChild>\r\n        <Button variant=\"ghost\" size=\"icon\">\r\n          <Languages className=\"h-4 w-4\" />\r\n          <span className=\"sr-only\">Select language</span>\r\n        </Button>\r\n      </DropdownMenuTrigger>\r\n      <DropdownMenuContent align=\"end\">\r\n        {languages.map((language) => (\r\n          <DropdownMenuItem\r\n            key={language.code}\r\n            onClick={() => setSelectedLanguage(language)}\r\n            className=\"flex items-center gap-2\"\r\n          >\r\n            <span>{language.flag}</span>\r\n            <span>{language.name}</span>\r\n          </DropdownMenuItem>\r\n        ))}\r\n      </DropdownMenuContent>\r\n    </DropdownMenu>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AALA;;;;;;AAYA,MAAM,YAAY;IAChB;QAAE,MAAM;QAAM,MAAM;QAAc,MAAM;IAAO;IAC/C;QAAE,MAAM;QAAM,MAAM;QAAW,MAAM;IAAO;IAC5C;QAAE,MAAM;QAAM,MAAM;QAAO,MAAM;IAAO;IACxC;QAAE,MAAM;QAAM,MAAM;QAAO,MAAM;IAAO;CACzC;AAEM,SAAS;IACd,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,SAAS,CAAC,EAAE;IAErE,qBACE,8OAAC,4IAAA,CAAA,eAAY;;0BACX,8OAAC,4IAAA,CAAA,sBAAmB;gBAAC,OAAO;0BAC1B,cAAA,8OAAC,kIAAA,CAAA,SAAM;oBAAC,SAAQ;oBAAQ,MAAK;;sCAC3B,8OAAC,4MAAA,CAAA,YAAS;4BAAC,WAAU;;;;;;sCACrB,8OAAC;4BAAK,WAAU;sCAAU;;;;;;;;;;;;;;;;;0BAG9B,8OAAC,4IAAA,CAAA,sBAAmB;gBAAC,OAAM;0BACxB,UAAU,GAAG,CAAC,CAAC,yBACd,8OAAC,4IAAA,CAAA,mBAAgB;wBAEf,SAAS,IAAM,oBAAoB;wBACnC,WAAU;;0CAEV,8OAAC;0CAAM,SAAS,IAAI;;;;;;0CACpB,8OAAC;0CAAM,SAAS,IAAI;;;;;;;uBALf,SAAS,IAAI;;;;;;;;;;;;;;;;AAW9B", "debugId": null}}, {"offset": {"line": 1308, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Project/Golang/hrm/next-client/src/components/ui/avatar.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as AvatarPrimitive from \"@radix-ui/react-avatar\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Avatar({\n  className,\n  ...props\n}: React.ComponentProps<typeof AvatarPrimitive.Root>) {\n  return (\n    <AvatarPrimitive.Root\n      data-slot=\"avatar\"\n      className={cn(\n        \"relative flex size-8 shrink-0 overflow-hidden rounded-full\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction AvatarImage({\n  className,\n  ...props\n}: React.ComponentProps<typeof AvatarPrimitive.Image>) {\n  return (\n    <AvatarPrimitive.Image\n      data-slot=\"avatar-image\"\n      className={cn(\"aspect-square size-full\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction AvatarFallback({\n  className,\n  ...props\n}: React.ComponentProps<typeof AvatarPrimitive.Fallback>) {\n  return (\n    <AvatarPrimitive.Fallback\n      data-slot=\"avatar-fallback\"\n      className={cn(\n        \"bg-muted flex size-full items-center justify-center rounded-full\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Avatar, AvatarImage, AvatarFallback }\n"], "names": [], "mappings": ";;;;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,OAAO,EACd,SAAS,EACT,GAAG,OAC+C;IAClD,qBACE,8OAAC,kKAAA,CAAA,OAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8DACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,8OAAC,kKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,2BAA2B;QACxC,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,eAAe,EACtB,SAAS,EACT,GAAG,OACmD;IACtD,qBACE,8OAAC,kKAAA,CAAA,WAAwB;QACvB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,oEACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 1360, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Project/Golang/hrm/next-client/src/components/header/user-avatar.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { LogOut, Settings, UserCircle } from \"lucide-react\";\r\nimport { But<PERSON> } from \"@/components/ui/button\";\r\nimport {\r\n  DropdownMenu,\r\n  DropdownMenuContent,\r\n  DropdownMenuItem,\r\n  DropdownMenuLabel,\r\n  DropdownMenuSeparator,\r\n  DropdownMenuTrigger,\r\n} from \"@/components/ui/dropdown-menu\";\r\nimport { Avatar, AvatarFallback, AvatarImage } from \"@/components/ui/avatar\";\r\n\r\nexport function UserAvatar() {\r\n  return (\r\n    <DropdownMenu>\r\n      <DropdownMenuTrigger asChild>\r\n        <Button variant=\"ghost\" className=\"relative h-8 w-8 rounded-full\">\r\n          <Avatar className=\"h-8 w-8\">\r\n            <AvatarImage src=\"/placeholder.svg?height=32&width=32\" alt=\"User\" />\r\n            <AvatarFallback>AD</AvatarFallback>\r\n          </Avatar>\r\n        </Button>\r\n      </DropdownMenuTrigger>\r\n      <DropdownMenuContent className=\"w-56\" align=\"end\" forceMount>\r\n        <DropdownMenuLabel className=\"font-normal\">\r\n          <div className=\"flex flex-col space-y-1\">\r\n            <p className=\"text-sm font-medium leading-none\">Admin User</p>\r\n            <p className=\"text-xs leading-none text-muted-foreground\">\r\n              <EMAIL>\r\n            </p>\r\n          </div>\r\n        </DropdownMenuLabel>\r\n        <DropdownMenuSeparator />\r\n        <DropdownMenuItem>\r\n          <UserCircle className=\"mr-2 h-4 w-4\" />\r\n          <span>Profile</span>\r\n        </DropdownMenuItem>\r\n        <DropdownMenuItem>\r\n          <Settings className=\"mr-2 h-4 w-4\" />\r\n          <span>Settings</span>\r\n        </DropdownMenuItem>\r\n        <DropdownMenuSeparator />\r\n        <DropdownMenuItem>\r\n          <LogOut className=\"mr-2 h-4 w-4\" />\r\n          <span>Log out</span>\r\n        </DropdownMenuItem>\r\n      </DropdownMenuContent>\r\n    </DropdownMenu>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AAAA;AACA;AACA;AAQA;AAZA;;;;;;AAcO,SAAS;IACd,qBACE,8OAAC,4IAAA,CAAA,eAAY;;0BACX,8OAAC,4IAAA,CAAA,sBAAmB;gBAAC,OAAO;0BAC1B,cAAA,8OAAC,kIAAA,CAAA,SAAM;oBAAC,SAAQ;oBAAQ,WAAU;8BAChC,cAAA,8OAAC,kIAAA,CAAA,SAAM;wBAAC,WAAU;;0CAChB,8OAAC,kIAAA,CAAA,cAAW;gCAAC,KAAI;gCAAsC,KAAI;;;;;;0CAC3D,8OAAC,kIAAA,CAAA,iBAAc;0CAAC;;;;;;;;;;;;;;;;;;;;;;0BAItB,8OAAC,4IAAA,CAAA,sBAAmB;gBAAC,WAAU;gBAAO,OAAM;gBAAM,UAAU;;kCAC1D,8OAAC,4IAAA,CAAA,oBAAiB;wBAAC,WAAU;kCAC3B,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAE,WAAU;8CAAmC;;;;;;8CAChD,8OAAC;oCAAE,WAAU;8CAA6C;;;;;;;;;;;;;;;;;kCAK9D,8OAAC,4IAAA,CAAA,wBAAqB;;;;;kCACtB,8OAAC,4IAAA,CAAA,mBAAgB;;0CACf,8OAAC,kNAAA,CAAA,aAAU;gCAAC,WAAU;;;;;;0CACtB,8OAAC;0CAAK;;;;;;;;;;;;kCAER,8OAAC,4IAAA,CAAA,mBAAgB;;0CACf,8OAAC,0MAAA,CAAA,WAAQ;gCAAC,WAAU;;;;;;0CACpB,8OAAC;0CAAK;;;;;;;;;;;;kCAER,8OAAC,4IAAA,CAAA,wBAAqB;;;;;kCACtB,8OAAC,4IAAA,CAAA,mBAAgB;;0CACf,8OAAC,0MAAA,CAAA,SAAM;gCAAC,WAAU;;;;;;0CAClB,8OAAC;0CAAK;;;;;;;;;;;;;;;;;;;;;;;;AAKhB", "debugId": null}}, {"offset": {"line": 1550, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Project/Golang/hrm/next-client/src/components/header/header-actions.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { <PERSON>ModeToggle } from \"./dark-mode-toggle\";\r\nimport { LanguageSelector } from \"./language-selector\";\r\nimport { UserAvatar } from \"./user-avatar\";\r\n\r\nexport function HeaderActions() {\r\n  return (\r\n    <div className=\"flex items-center space-x-2\">\r\n      <DarkModeToggle />\r\n      <LanguageSelector />\r\n      <UserAvatar />\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAMO,SAAS;IACd,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,sJAAA,CAAA,iBAAc;;;;;0BACf,8OAAC,oJAAA,CAAA,mBAAgB;;;;;0BACjB,8OAAC,8IAAA,CAAA,aAAU;;;;;;;;;;;AAGjB", "debugId": null}}, {"offset": {"line": 1594, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Project/Golang/hrm/next-client/src/components/header/header.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { Logo } from \"./logo\";\r\nimport { Navigation } from \"./navigation\";\r\nimport { HeaderActions } from \"./header-actions\";\r\n\r\ninterface HeaderProps {\r\n  activeSection: string;\r\n  onSectionChange: (section: string) => void;\r\n}\r\n\r\nexport function Header({ activeSection, onSectionChange }: HeaderProps) {\r\n  return (\r\n    <header className=\"sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60\">\r\n      <div className=\"w-full flex h-16 items-center justify-between px-4\">\r\n        <Logo />\r\n        <Navigation\r\n          activeSection={activeSection}\r\n          onSectionChange={onSectionChange}\r\n        />\r\n        <HeaderActions />\r\n      </div>\r\n    </header>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAWO,SAAS,OAAO,EAAE,aAAa,EAAE,eAAe,EAAe;IACpE,qBACE,8OAAC;QAAO,WAAU;kBAChB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC,oIAAA,CAAA,OAAI;;;;;8BACL,8OAAC,0IAAA,CAAA,aAAU;oBACT,eAAe;oBACf,iBAAiB;;;;;;8BAEnB,8OAAC,iJAAA,CAAA,gBAAa;;;;;;;;;;;;;;;;AAItB", "debugId": null}}, {"offset": {"line": 1648, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Project/Golang/hrm/next-client/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card\"\n      className={cn(\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-header\"\n      className={cn(\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-title\"\n      className={cn(\"leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-action\"\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-content\"\n      className={cn(\"px-6\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-footer\"\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 1745, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Project/Golang/hrm/next-client/src/app/dashboard/main-content.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport {\r\n  <PERSON>,\r\n  CardContent,\r\n  CardDescription,\r\n  CardHeader,\r\n  CardTitle,\r\n} from \"@/components/ui/card\";\r\nimport { BarChart3, Users, TrendingUp, Activity } from \"lucide-react\";\r\n\r\ninterface MainContentProps {\r\n  activeSection: string;\r\n}\r\n\r\nexport function MainContent({ activeSection }: MainContentProps) {\r\n  const renderContent = () => {\r\n    switch (activeSection) {\r\n      case \"Dashboard\":\r\n        return (\r\n          <div className=\"grid gap-4 md:grid-cols-2 lg:grid-cols-4\">\r\n            <Card>\r\n              <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\r\n                <CardTitle className=\"text-sm font-medium\">\r\n                  Total Users\r\n                </CardTitle>\r\n                <Users className=\"h-4 w-4 text-muted-foreground\" />\r\n              </CardHeader>\r\n              <CardContent>\r\n                <div className=\"text-2xl font-bold\">2,350</div>\r\n                <p className=\"text-xs text-muted-foreground\">\r\n                  +20.1% from last month\r\n                </p>\r\n              </CardContent>\r\n            </Card>\r\n            <Card>\r\n              <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\r\n                <CardTitle className=\"text-sm font-medium\">Revenue</CardTitle>\r\n                <TrendingUp className=\"h-4 w-4 text-muted-foreground\" />\r\n              </CardHeader>\r\n              <CardContent>\r\n                <div className=\"text-2xl font-bold\">$45,231.89</div>\r\n                <p className=\"text-xs text-muted-foreground\">\r\n                  +20.1% from last month\r\n                </p>\r\n              </CardContent>\r\n            </Card>\r\n            <Card>\r\n              <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\r\n                <CardTitle className=\"text-sm font-medium\">\r\n                  Active Sessions\r\n                </CardTitle>\r\n                <Activity className=\"h-4 w-4 text-muted-foreground\" />\r\n              </CardHeader>\r\n              <CardContent>\r\n                <div className=\"text-2xl font-bold\">573</div>\r\n                <p className=\"text-xs text-muted-foreground\">\r\n                  +201 since last hour\r\n                </p>\r\n              </CardContent>\r\n            </Card>\r\n            <Card>\r\n              <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\r\n                <CardTitle className=\"text-sm font-medium\">\r\n                  Conversion Rate\r\n                </CardTitle>\r\n                <BarChart3 className=\"h-4 w-4 text-muted-foreground\" />\r\n              </CardHeader>\r\n              <CardContent>\r\n                <div className=\"text-2xl font-bold\">12.5%</div>\r\n                <p className=\"text-xs text-muted-foreground\">\r\n                  +2.5% from last month\r\n                </p>\r\n              </CardContent>\r\n            </Card>\r\n          </div>\r\n        );\r\n      case \"Analytics\":\r\n        return (\r\n          <Card>\r\n            <CardHeader>\r\n              <CardTitle>Analytics Overview</CardTitle>\r\n              <CardDescription>Detailed analytics and insights</CardDescription>\r\n            </CardHeader>\r\n            <CardContent>\r\n              <p>Analytics content will be displayed here...</p>\r\n            </CardContent>\r\n          </Card>\r\n        );\r\n      case \"Users\":\r\n        return (\r\n          <Card>\r\n            <CardHeader>\r\n              <CardTitle>User Management</CardTitle>\r\n              <CardDescription>\r\n                Manage system users and permissions\r\n              </CardDescription>\r\n            </CardHeader>\r\n            <CardContent>\r\n              <p>User management interface will be displayed here...</p>\r\n            </CardContent>\r\n          </Card>\r\n        );\r\n      case \"Reports\":\r\n        return (\r\n          <Card>\r\n            <CardHeader>\r\n              <CardTitle>Reports</CardTitle>\r\n              <CardDescription>\r\n                Generate and view system reports\r\n              </CardDescription>\r\n            </CardHeader>\r\n            <CardContent>\r\n              <p>Reports interface will be displayed here...</p>\r\n            </CardContent>\r\n          </Card>\r\n        );\r\n      case \"Settings\":\r\n        return (\r\n          <Card>\r\n            <CardHeader>\r\n              <CardTitle>System Settings</CardTitle>\r\n              <CardDescription>Configure system preferences</CardDescription>\r\n            </CardHeader>\r\n            <CardContent>\r\n              <p>Settings interface will be displayed here...</p>\r\n            </CardContent>\r\n          </Card>\r\n        );\r\n      default:\r\n        return <div>Content not found</div>;\r\n    }\r\n  };\r\n\r\n  return (\r\n    <main className=\"flex-1 container mx-auto px-4 py-6\">\r\n      <div className=\"mb-6\">\r\n        <h1 className=\"text-3xl font-bold tracking-tight\">{activeSection}</h1>\r\n        <p className=\"text-muted-foreground\">\r\n          Welcome to your {activeSection.toLowerCase()} section\r\n        </p>\r\n      </div>\r\n      {renderContent()}\r\n    </main>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AAOA;AAAA;AAAA;AAAA;AATA;;;;AAeO,SAAS,YAAY,EAAE,aAAa,EAAoB;IAC7D,MAAM,gBAAgB;QACpB,OAAQ;YACN,KAAK;gBACH,qBACE,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,gIAAA,CAAA,OAAI;;8CACH,8OAAC,gIAAA,CAAA,aAAU;oCAAC,WAAU;;sDACpB,8OAAC,gIAAA,CAAA,YAAS;4CAAC,WAAU;sDAAsB;;;;;;sDAG3C,8OAAC,oMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;;;;;;;8CAEnB,8OAAC,gIAAA,CAAA,cAAW;;sDACV,8OAAC;4CAAI,WAAU;sDAAqB;;;;;;sDACpC,8OAAC;4CAAE,WAAU;sDAAgC;;;;;;;;;;;;;;;;;;sCAKjD,8OAAC,gIAAA,CAAA,OAAI;;8CACH,8OAAC,gIAAA,CAAA,aAAU;oCAAC,WAAU;;sDACpB,8OAAC,gIAAA,CAAA,YAAS;4CAAC,WAAU;sDAAsB;;;;;;sDAC3C,8OAAC,kNAAA,CAAA,aAAU;4CAAC,WAAU;;;;;;;;;;;;8CAExB,8OAAC,gIAAA,CAAA,cAAW;;sDACV,8OAAC;4CAAI,WAAU;sDAAqB;;;;;;sDACpC,8OAAC;4CAAE,WAAU;sDAAgC;;;;;;;;;;;;;;;;;;sCAKjD,8OAAC,gIAAA,CAAA,OAAI;;8CACH,8OAAC,gIAAA,CAAA,aAAU;oCAAC,WAAU;;sDACpB,8OAAC,gIAAA,CAAA,YAAS;4CAAC,WAAU;sDAAsB;;;;;;sDAG3C,8OAAC,0MAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;;;;;;;8CAEtB,8OAAC,gIAAA,CAAA,cAAW;;sDACV,8OAAC;4CAAI,WAAU;sDAAqB;;;;;;sDACpC,8OAAC;4CAAE,WAAU;sDAAgC;;;;;;;;;;;;;;;;;;sCAKjD,8OAAC,gIAAA,CAAA,OAAI;;8CACH,8OAAC,gIAAA,CAAA,aAAU;oCAAC,WAAU;;sDACpB,8OAAC,gIAAA,CAAA,YAAS;4CAAC,WAAU;sDAAsB;;;;;;sDAG3C,8OAAC,kNAAA,CAAA,YAAS;4CAAC,WAAU;;;;;;;;;;;;8CAEvB,8OAAC,gIAAA,CAAA,cAAW;;sDACV,8OAAC;4CAAI,WAAU;sDAAqB;;;;;;sDACpC,8OAAC;4CAAE,WAAU;sDAAgC;;;;;;;;;;;;;;;;;;;;;;;;YAOvD,KAAK;gBACH,qBACE,8OAAC,gIAAA,CAAA,OAAI;;sCACH,8OAAC,gIAAA,CAAA,aAAU;;8CACT,8OAAC,gIAAA,CAAA,YAAS;8CAAC;;;;;;8CACX,8OAAC,gIAAA,CAAA,kBAAe;8CAAC;;;;;;;;;;;;sCAEnB,8OAAC,gIAAA,CAAA,cAAW;sCACV,cAAA,8OAAC;0CAAE;;;;;;;;;;;;;;;;;YAIX,KAAK;gBACH,qBACE,8OAAC,gIAAA,CAAA,OAAI;;sCACH,8OAAC,gIAAA,CAAA,aAAU;;8CACT,8OAAC,gIAAA,CAAA,YAAS;8CAAC;;;;;;8CACX,8OAAC,gIAAA,CAAA,kBAAe;8CAAC;;;;;;;;;;;;sCAInB,8OAAC,gIAAA,CAAA,cAAW;sCACV,cAAA,8OAAC;0CAAE;;;;;;;;;;;;;;;;;YAIX,KAAK;gBACH,qBACE,8OAAC,gIAAA,CAAA,OAAI;;sCACH,8OAAC,gIAAA,CAAA,aAAU;;8CACT,8OAAC,gIAAA,CAAA,YAAS;8CAAC;;;;;;8CACX,8OAAC,gIAAA,CAAA,kBAAe;8CAAC;;;;;;;;;;;;sCAInB,8OAAC,gIAAA,CAAA,cAAW;sCACV,cAAA,8OAAC;0CAAE;;;;;;;;;;;;;;;;;YAIX,KAAK;gBACH,qBACE,8OAAC,gIAAA,CAAA,OAAI;;sCACH,8OAAC,gIAAA,CAAA,aAAU;;8CACT,8OAAC,gIAAA,CAAA,YAAS;8CAAC;;;;;;8CACX,8OAAC,gIAAA,CAAA,kBAAe;8CAAC;;;;;;;;;;;;sCAEnB,8OAAC,gIAAA,CAAA,cAAW;sCACV,cAAA,8OAAC;0CAAE;;;;;;;;;;;;;;;;;YAIX;gBACE,qBAAO,8OAAC;8BAAI;;;;;;QAChB;IACF;IAEA,qBACE,8OAAC;QAAK,WAAU;;0BACd,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAqC;;;;;;kCACnD,8OAAC;wBAAE,WAAU;;4BAAwB;4BAClB,cAAc,WAAW;4BAAG;;;;;;;;;;;;;YAGhD;;;;;;;AAGP", "debugId": null}}, {"offset": {"line": 2227, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Project/Golang/hrm/next-client/src/app/dashboard/page.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { Footer } from \"@/components/footer/footer\";\r\nimport { Header } from \"@/components/header/header\";\r\nimport { useState } from \"react\";\r\nimport { MainContent } from \"./main-content\";\r\n\r\nexport default function DashboardPage() {\r\n  const [activeSection, setActiveSection] = useState(\"Dashboard\");\r\n\r\n  return (\r\n    <div className=\"min-h-screen flex flex-col bg-background\">\r\n      <Header\r\n        activeSection={activeSection}\r\n        onSectionChange={setActiveSection}\r\n      />\r\n      <MainContent activeSection={activeSection} />\r\n      <Footer activeSection={activeSection} />\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AALA;;;;;;AAOe,SAAS;IACtB,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEnD,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,sIAAA,CAAA,SAAM;gBACL,eAAe;gBACf,iBAAiB;;;;;;0BAEnB,8OAAC,2IAAA,CAAA,cAAW;gBAAC,eAAe;;;;;;0BAC5B,8OAAC,sIAAA,CAAA,SAAM;gBAAC,eAAe;;;;;;;;;;;;AAG7B", "debugId": null}}]}