// repositories/user_repository.go - Tối ưu queries với SELECT đúng columns
package repositories

import (
	"database/sql"
	"go-api/database"
	"go-api/models"
	"time"
)

// UserRepository - Struct quản lý các thao tác database với user
type UserRepository struct{}

// NewUserRepository - Constructor tạo UserRepository mới
func NewUserRepository() *UserRepository {
	return &UserRepository{}
}

// CreateUser - Tạo user mới trong database
func (r *UserRepository) CreateUser(userAuth *models.UserAuth) error {
	// Query SQL sử dụng named parameters (@parameter)
	query := `
		INSERT INTO users (userid, password_hash, fullname, email, created_at, is_active, role)
		VALUES (@userid, @password_hash, @fullname, @email, @created_at, @is_active, @role)
	`
	
	// Thiết lập giá trị mặc định
	userAuth.CreatedAt = time.Now()  // Thời gian hiện tại
	userAuth.IsActive = true         // Kích hoạt ngay
	if userAuth.Role == "" {
		userAuth.Role = "user"       // Role mặc định
	}

	// Thực thi query với named parameters
	_, err := database.DB.NamedExec(query, userAuth)
	return err
}

// GetUserByUserID - Lấy thông tin user cơ bản (cho API response)
// SELECT chỉ những columns cần thiết để tối ưu hiệu suất
func (r *UserRepository) GetUserByUserID(userid string) (*models.User, error) {
	var user models.User
	
	// Query chỉ SELECT các fields cần thiết cho response
	query := `
		SELECT id, userid, fullname, email, created_at, is_active, role
		FROM users 
		WHERE userid = @userid AND is_active = 1
	`
	
	// Sử dụng sqlx.Get để lấy 1 record
	err := database.DB.Get(&user, query, sql.Named("userid", userid))
	if err != nil {
		return nil, err
	}
	
	return &user, nil
}

// GetUserAuthByUserID - Lấy thông tin user để authentication (có password_hash)
// Chỉ dùng cho việc đăng nhập, validation token
func (r *UserRepository) GetUserAuthByUserID(userid string) (*models.UserAuth, error) {
	var userAuth models.UserAuth
	
	// Query SELECT tất cả columns cần thiết cho authentication
	query := `
		SELECT id, userid, password_hash, fullname, email, created_at, is_active, 
		       refresh_token, refresh_token_expires, role
		FROM users 
		WHERE userid = @userid AND is_active = 1
	`
	
	err := database.DB.Get(&userAuth, query, sql.Named("userid", userid))
	if err != nil {
		return nil, err
	}
	
	return &userAuth, nil
}

// GetAllUsers - Lấy danh sách tất cả users (admin only)
// SELECT chỉ thông tin cần thiết cho danh sách
func (r *UserRepository) GetAllUsers() ([]models.User, error) {
	var users []models.User
	
	// Query tối ưu chỉ lấy fields hiển thị
	query := `
		SELECT id, userid, fullname, email, created_at, is_active, role
		FROM users 
		WHERE is_active = 1
		ORDER BY created_at DESC
	`
	
	err := database.DB.Select(&users, query)
	if err != nil {
		return nil, err
	}
	
	return users, nil
}

// UpdateRefreshToken - Cập nhật refresh token khi đăng nhập
func (r *UserRepository) UpdateRefreshToken(userid, refreshToken string, expires time.Time) error {
	query := `
		UPDATE users 
		SET refresh_token = @refresh_token, refresh_token_expires = @expires
		WHERE userid = @userid
	`
	
	// Thực thi update với named parameters
	_, err := database.DB.Exec(query,
		sql.Named("refresh_token", refreshToken),
		sql.Named("expires", expires),
		sql.Named("userid", userid))
	
	return err
}

// ValidateRefreshToken - Kiểm tra refresh token có hợp lệ không
func (r *UserRepository) ValidateRefreshToken(refreshToken string) (*models.UserAuth, error) {
	var userAuth models.UserAuth
	
	// Query kiểm tra token và thời gian hết hạn
	query := `
		SELECT id, userid, password_hash, fullname, email, created_at, is_active, 
		       refresh_token, refresh_token_expires, role
		FROM users 
		WHERE refresh_token = @refresh_token 
		AND refresh_token_expires > GETDATE()  -- Chưa hết hạn
		AND is_active = 1                      -- User đang active
	`
	
	err := database.DB.Get(&userAuth, query, sql.Named("refresh_token", refreshToken))
	if err != nil {
		return nil, err
	}
	
	return &userAuth, nil
}

// UpdateProfile - Cập nhật thông tin cá nhân
func (r *UserRepository) UpdateProfile(userid, fullname, email string) error {
	query := `
		UPDATE users 
		SET fullname = @fullname, email = @email
		WHERE userid = @userid AND is_active = 1
	`
	
	_, err := database.DB.Exec(query,
		sql.Named("fullname", fullname),
		sql.Named("email", email),
		sql.Named("userid", userid))
	
	return err
}

// SoftDeleteUser - Xóa mềm user (set is_active = 0)
func (r *UserRepository) SoftDeleteUser(userid string) error {
	query := `
		UPDATE users 
		SET is_active = 0, refresh_token = NULL, refresh_token_expires = NULL
		WHERE userid = @userid
	`
	
	_, err := database.DB.Exec(query, sql.Named("userid", userid))
	return err
}

// UpdateUserRole - Cập nhật role của user
func (r *UserRepository) UpdateUserRole(userid, role string) error {
	query := `
		UPDATE users 
		SET role = @role
		WHERE userid = @userid AND is_active = 1
	`
	
	_, err := database.DB.Exec(query,
		sql.Named("role", role),
		sql.Named("userid", userid))
	
	return err
}