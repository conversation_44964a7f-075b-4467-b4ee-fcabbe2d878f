package routes

import (
	"go-api/handlers"

	"github.com/gin-gonic/gin"
)

// RegisterRoutes - Đ<PERSON>ng ký tất cả routes cho ứng dụng
func RegisterRoutes(r *gin.Engine) {
	// Middleware global cho tất cả routes
	r.Use(gin.Logger())     // Log các requests
	r.Use(gin.Recovery())   // Phục hồi khi panic
	r.Use(corsMiddleware()) // Xử lý CORS cho frontend

	// Health check endpoint
	r.GET("/health", func(c *gin.Context) {
		c.JSON(200, gin.H{
			"status":  "OK",
			"message": "API đang hoạt động bình thường",
		})
	})

	api := r.Group("/api")
	
	// ========== AUTHENTICATION ROUTES (Public) ==========
	// Không yêu cầu authentication
	auth := api.Group("/auth")
	{
		auth.POST("/register", handlers.Register)      // Đăng ký tài khoản mới
		auth.POST("/login", handlers.Login)            // Đăng nhập (set refresh token vào cookie)
		auth.POST("/refresh", handlers.RefreshToken)   // Làm mới access token từ cookie
		auth.POST("/logout", handlers.Logout)          // Đăng xuất (xóa cookie)
	}
}

// corsMiddleware - Xử lý Cross-Origin Resource Sharing
func corsMiddleware() gin.HandlerFunc {
	return gin.HandlerFunc(func(c *gin.Context) {
		// Cho phép frontend gọi API từ domain khác
		c.Header("Access-Control-Allow-Origin", "*") // Trong production nên chỉ định domain cụ thể
		c.Header("Access-Control-Allow-Credentials", "true")
		c.Header("Access-Control-Allow-Headers", "Content-Type, Content-Length, Accept-Encoding, X-CSRF-Token, Authorization, accept, origin, Cache-Control, X-Requested-With")
		c.Header("Access-Control-Allow-Methods", "POST, HEAD, PATCH, OPTIONS, GET, PUT, DELETE")

		// Xử lý preflight requests
		if c.Request.Method == "OPTIONS" {
			c.AbortWithStatus(204)
			return
		}

		c.Next()
	})
}