"use client";

import { Footer } from "@/components/footer/footer";
import { Header } from "@/components/header/header";
import { useState } from "react";
import { MainContent } from "./main-content";

export default function DashboardPage() {
  const [activeSection, setActiveSection] = useState("Dashboard");

  return (
    <div className="min-h-screen flex flex-col bg-background">
      <Header
        activeSection={activeSection}
        onSectionChange={setActiveSection}
      />
      <MainContent activeSection={activeSection} />
      <Footer activeSection={activeSection} />
    </div>
  );
}
